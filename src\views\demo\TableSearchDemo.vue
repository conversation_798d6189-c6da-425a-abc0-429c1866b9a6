<template>
  <div class="table-search-demo">
    <h2>表格搜索组件演示</h2>
    <p>参考 vben 的表格和搜索项实现，支持动态展开收起、固定高度表格、响应式布局</p>
    
    <!-- 演示1：基础用法 -->
    <div class="demo-section">
      <h3>基础用法</h3>
      <YdTableWithSearch
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :search-fields="searchFields"
        :search-data="searchForm"
        :total="total"
        :current-page="currentPage"
        :page-size="pageSize"
        :show-search-collapse="true"
        :search-collapsed="true"
        :search-collapsed-rows="1"
        :search-cols-per-row="3"
        :height-strategy="'fixed'"
        :fixed-height="500"
        :show-selection="true"
        highlight-current-row
        :row-clickable="true"
        @search="handleSearch"
        @reset="handleReset"
        @page-change="handlePageChange"
        @size-change="handleSizeChange"
        @row-click="handleRowClick"
        @selection-change="handleSelectionChange"
      />
    </div>

    <!-- 演示2：视口高度 -->
    <div class="demo-section">
      <h3>视口高度模式</h3>
      <YdTableWithSearch
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :search-fields="searchFields"
        :search-data="searchForm2"
        :total="total"
        :current-page="currentPage2"
        :page-size="pageSize2"
        :show-search-collapse="true"
        :search-collapsed="false"
        :search-collapsed-rows="2"
        :search-cols-per-row="4"
        :height-strategy="'viewport'"
        :show-selection="false"
        @search="handleSearch2"
        @reset="handleReset2"
        @page-change="handlePageChange2"
        @size-change="handleSizeChange2"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import YdTableWithSearch from '@/components/common/YdTableWithSearch.vue'
import { ElMessage } from 'element-plus'

defineOptions({
  name: 'TableSearchDemo',
})

// 表格列配置
const columns = [
  {
    title: '序号',
    dataIndex: 'id',
    width: 80,
    align: 'center',
  },
  {
    title: '公告标题',
    dataIndex: 'title',
    minWidth: 200,
    type: 'link',
  },
  {
    title: '采购人',
    dataIndex: 'purchaser',
    width: 150,
  },
  {
    title: '公告类型',
    dataIndex: 'type',
    width: 120,
    type: 'tag',
    tagMap: {
      '招标公告': { type: 'primary', text: '招标公告' },
      '中标公告': { type: 'success', text: '中标公告' },
      '更正公告': { type: 'warning', text: '更正公告' },
      '废标公告': { type: 'danger', text: '废标公告' },
    },
  },
  {
    title: '发布时间',
    dataIndex: 'publishTime',
    width: 120,
    sortable: true,
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    type: 'tag',
    tagMap: {
      '进行中': { type: 'success', text: '进行中' },
      '已结束': { type: 'info', text: '已结束' },
      '已暂停': { type: 'warning', text: '已暂停' },
    },
  },
]

// 搜索字段配置
const searchFields = [
  {
    name: 'title',
    label: '公告标题',
    type: 'text',
    placeholder: '请输入公告标题',
  },
  {
    name: 'purchaser',
    label: '采购人',
    type: 'text',
    placeholder: '请输入采购人',
  },
  {
    name: 'type',
    label: '公告类型',
    type: 'select',
    options: [
      { label: '招标公告', value: '招标公告' },
      { label: '中标公告', value: '中标公告' },
      { label: '更正公告', value: '更正公告' },
      { label: '废标公告', value: '废标公告' },
    ],
  },
  {
    name: 'status',
    label: '状态',
    type: 'select',
    options: [
      { label: '进行中', value: '进行中' },
      { label: '已结束', value: '已结束' },
      { label: '已暂停', value: '已暂停' },
    ],
  },
  {
    name: 'dateRange',
    label: '发布时间',
    type: 'daterange',
  },
  {
    name: 'amount',
    label: '预算金额',
    type: 'number',
    placeholder: '请输入预算金额',
  },
]

// 状态管理
const loading = ref(false)
const tableData = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const currentPage2 = ref(1)
const pageSize2 = ref(20)

// 搜索表单数据
const searchForm = reactive({})
const searchForm2 = reactive({})

// 模拟数据
const mockData = Array.from({ length: 100 }, (_, index) => ({
  id: index + 1,
  title: `招标公告标题 ${index + 1} - 这是一个很长的标题用来测试显示效果`,
  purchaser: `采购单位${index + 1}`,
  type: ['招标公告', '中标公告', '更正公告', '废标公告'][index % 4],
  publishTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
  status: ['进行中', '已结束', '已暂停'][index % 3],
  amount: Math.floor(Math.random() * 1000000) + 100000,
}))

// 事件处理
const handleSearch = (formData) => {
  console.log('搜索:', formData)
  ElMessage.success('搜索成功')
  loadData()
}

const handleReset = (formData) => {
  console.log('重置:', formData)
  ElMessage.info('已重置搜索条件')
  loadData()
}

const handleSearch2 = (formData) => {
  console.log('搜索2:', formData)
  ElMessage.success('搜索成功')
}

const handleReset2 = (formData) => {
  console.log('重置2:', formData)
  ElMessage.info('已重置搜索条件')
}

const handlePageChange = (page, size) => {
  currentPage.value = page
  console.log('页面变化:', page, size)
  loadData()
}

const handleSizeChange = (size, page) => {
  pageSize.value = size
  currentPage.value = page
  console.log('页面大小变化:', size, page)
  loadData()
}

const handlePageChange2 = (page, size) => {
  currentPage2.value = page
  console.log('页面变化2:', page, size)
}

const handleSizeChange2 = (size, page) => {
  pageSize2.value = size
  currentPage2.value = page
  console.log('页面大小变化2:', size, page)
}

const handleRowClick = (row) => {
  console.log('行点击:', row)
  ElMessage.info(`点击了第 ${row.id} 行`)
}

const handleSelectionChange = (selection) => {
  console.log('选择变化:', selection)
  ElMessage.info(`已选择 ${selection.length} 项`)
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    const start = (currentPage.value - 1) * pageSize.value
    const end = start + pageSize.value
    tableData.value = mockData.slice(start, end)
    total.value = mockData.length
  } finally {
    loading.value = false
  }
}

// 初始化
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.table-search-demo {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.demo-section {
  margin-bottom: 40px;
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.demo-section h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

h2 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 24px;
  font-weight: 700;
}

p {
  margin: 0 0 30px 0;
  color: #666;
  font-size: 14px;
  line-height: 1.6;
}
</style>
