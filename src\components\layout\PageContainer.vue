<template>
  <div class="page-container">
    <!-- 页面头部 -->
    <div
      v-if="
        description ||
        $slots.description ||
        title ||
        $slots.title ||
        $slots.extra
      "
      ref="headerRef"
      class="page-header"
    >
      <div class="page-header-content">
        <slot name="title">
          <div v-if="title" class="page-title">
            {{ title }}
          </div>
        </slot>

        <slot name="description">
          <p v-if="description" class="page-description">
            {{ description }}
          </p>
        </slot>
      </div>

      <div v-if="$slots.extra">
        <slot name="extra"></slot>
      </div>
    </div>

    <!-- 页面内容 -->
    <div class="page-content" :style="contentStyle">
      <slot></slot>
    </div>

    <!-- 页面底部 -->
    <div
      v-if="$slots.footer"
      ref="footerRef"
      class="page-footer"
    >
      <slot name="footer"></slot>
    </div>
  </div>
</template>

<script setup>
import { computed, nextTick, onMounted, ref, useTemplateRef } from 'vue'
import { CSS_VARIABLE_LAYOUT_CONTENT_HEIGHT } from '@/constants/globals'

defineOptions({
  name: 'PageContainer',
})

const props = defineProps({
  title: {
    type: String,
    default: '',
  },
  description: {
    type: String,
    default: '',
  },
  contentClass: {
    type: String,
    default: '',
  },
  /**
   * 根据content可见高度自适应
   */
  autoContentHeight: {
    type: Boolean,
    default: false,
  },
  headerClass: {
    type: String,
    default: '',
  },
  footerClass: {
    type: String,
    default: '',
  },
  /**
   * 自定义高度偏移量（像素）
   * @default 0
   */
  heightOffset: {
    type: Number,
    default: 0,
  },
})

const headerHeight = ref(0)
const footerHeight = ref(0)
const shouldAutoHeight = ref(false)

const headerRef = useTemplateRef('headerRef')
const footerRef = useTemplateRef('footerRef')

const contentStyle = computed(() => {
  if (props.autoContentHeight) {
    return {
      height: `calc(var(${CSS_VARIABLE_LAYOUT_CONTENT_HEIGHT}) - ${headerHeight.value}px - ${footerHeight.value}px - ${props.heightOffset}px)`,
      overflowY: shouldAutoHeight.value ? 'auto' : 'unset',
    }
  }
  return {}
})

async function calcContentHeight() {
  if (!props.autoContentHeight) {
    return
  }
  
  await nextTick()
  headerHeight.value = headerRef.value?.offsetHeight || 0
  footerHeight.value = footerRef.value?.offsetHeight || 0
  
  // 延迟启用自动高度，避免闪烁
  setTimeout(() => {
    shouldAutoHeight.value = true
  }, 30)
}

onMounted(() => {
  calcContentHeight()
})
</script>

<style scoped>
.page-container {
  position: relative;
  height: 100%;
}

.page-header {
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: flex-end;
  padding: 16px 24px;
  position: relative;
}

.page-header-content {
  flex: 1;
}

.page-title {
  margin-bottom: 8px;
  font-size: 18px;
  font-weight: 600;
  line-height: 1.5;
  color: #1f2937;
}

.page-description {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
}

.page-content {
  padding: 16px 24px;
  background: #fff;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.page-footer {
  background: #fff;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  padding: 16px 24px;
  border-top: 1px solid #f0f0f0;
}
</style>
})

const slots = useSlots()
const headerRef = ref(null)
const footerRef = ref(null)

const headerHeight = ref(0)
const footerHeight = ref(0)
const shouldAutoHeight = ref(false)

// 判断是否有头部和底部
const hasHeader = computed(() => {
  return props.title || props.description || slots.header
})

const hasFooter = computed(() => {
  return slots.footer
})

// 内容区域样式
const contentStyle = computed(() => {
  if (props.autoContentHeight && shouldAutoHeight.value) {
    const offset = headerHeight.value + footerHeight.value + props.heightOffset
    return {
      height: `calc(var(--vben-content-height) - ${offset}px)`,
      overflowY: 'auto',
    }
  }
  return {}
})

// 计算内容高度
async function calcContentHeight() {
  if (!props.autoContentHeight) {
    return
  }
  
  await nextTick()
  headerHeight.value = headerRef.value?.offsetHeight || 0
  footerHeight.value = footerRef.value?.offsetHeight || 0
  
  // 延迟启用自动高度，确保计算准确
  setTimeout(() => {
    shouldAutoHeight.value = true
  }, 30)
}

onMounted(() => {
  calcContentHeight()
})
</script>

<style scoped>
.page-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.page-header {
  flex-shrink: 0;
  background: #fff;
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
}

.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 8px;
}

.page-description {
  color: #8c8c8c;
  font-size: 14px;
}

.page-content {
  flex: 1;
  min-height: 0;
}

.page-footer {
  flex-shrink: 0;
  background: #fff;
  padding: 16px 24px;
  border-top: 1px solid #f0f0f0;
}
</style>
