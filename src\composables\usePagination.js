import { reactive, computed } from 'vue'

/**
 * 分页组合式函数
 * @param {Object} options 配置选项
 * @param {number} options.defaultPageSize 默认每页条数
 * @param {Array} options.pageSizes 可选的每页条数
 * @param {Function} options.onPageChange 页面变化回调
 * @param {Function} options.onSizeChange 每页条数变化回调
 */
export function usePagination(options = {}) {
  const {
    defaultPageSize = 10,
    pageSizes = [10, 20, 50, 100],
    onPageChange,
    onSizeChange,
  } = options

  // 分页状态
  const pagination = reactive({
    currentPage: 1,
    pageSize: defaultPageSize,
    total: 0,
  })

  // 计算属性
  const totalPages = computed(() => {
    return Math.ceil(pagination.total / pagination.pageSize)
  })

  const startIndex = computed(() => {
    return (pagination.currentPage - 1) * pagination.pageSize
  })

  const endIndex = computed(() => {
    return Math.min(startIndex.value + pagination.pageSize, pagination.total)
  })

  const hasNextPage = computed(() => {
    return pagination.currentPage < totalPages.value
  })

  const hasPrevPage = computed(() => {
    return pagination.currentPage > 1
  })

  // 分页信息文本
  const paginationInfo = computed(() => {
    if (pagination.total === 0) {
      return '共 0 条数据'
    }
    return `共 ${pagination.total} 条数据`
  })

  // 当前页范围信息
  const rangeInfo = computed(() => {
    if (pagination.total === 0) {
      return '0-0'
    }
    return `${startIndex.value + 1}-${endIndex.value}`
  })

  // 方法
  const handleCurrentChange = (page) => {
    if (page >= 1 && page <= totalPages.value) {
      pagination.currentPage = page
      onPageChange?.(page, pagination.pageSize)
    }
  }

  const handleSizeChange = (size) => {
    pagination.pageSize = size
    // 重新计算当前页，确保不超出范围
    const maxPage = Math.ceil(pagination.total / size)
    if (pagination.currentPage > maxPage) {
      pagination.currentPage = Math.max(1, maxPage)
    }
    onSizeChange?.(size, pagination.currentPage)
  }

  const setTotal = (total) => {
    pagination.total = total
    // 如果当前页超出范围，重置到最后一页
    const maxPage = Math.ceil(total / pagination.pageSize)
    if (pagination.currentPage > maxPage && maxPage > 0) {
      pagination.currentPage = maxPage
    }
  }

  const reset = () => {
    pagination.currentPage = 1
    pagination.total = 0
  }

  const goToPage = (page) => {
    handleCurrentChange(page)
  }

  const nextPage = () => {
    if (hasNextPage.value) {
      handleCurrentChange(pagination.currentPage + 1)
    }
  }

  const prevPage = () => {
    if (hasPrevPage.value) {
      handleCurrentChange(pagination.currentPage - 1)
    }
  }

  const goToFirstPage = () => {
    handleCurrentChange(1)
  }

  const goToLastPage = () => {
    handleCurrentChange(totalPages.value)
  }

  // 获取当前页的数据切片（用于前端分页）
  const getPageData = (data) => {
    if (!Array.isArray(data)) return []
    const start = startIndex.value
    const end = start + pagination.pageSize
    return data.slice(start, end)
  }

  return {
    // 状态
    pagination,
    pageSizes,
    
    // 计算属性
    totalPages,
    startIndex,
    endIndex,
    hasNextPage,
    hasPrevPage,
    paginationInfo,
    rangeInfo,
    
    // 方法
    handleCurrentChange,
    handleSizeChange,
    setTotal,
    reset,
    goToPage,
    nextPage,
    prevPage,
    goToFirstPage,
    goToLastPage,
    getPageData,
  }
}
