import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import { createPinia } from 'pinia'
import { useAuthStore } from './store/modules/auth'

const app = createApp(App)
const pinia = createPinia()
import './index.css'
import '@/assets/styles/_utils.scss'
import 'element-plus/dist/index.css'
app.use(pinia)
app.use(router)

// 初始化认证状态
const authStore = useAuthStore()
authStore.initAuth()

app.mount('#root')
