/**
 * 搜索表单字段类型定义
 */

/**
 * 基础字段配置
 * @typedef {Object} BaseField
 * @property {string} prop - 字段属性名
 * @property {string} label - 字段标签
 * @property {string} type - 字段类型
 * @property {string} [placeholder] - 占位符文本
 * @property {boolean} [clearable=true] - 是否可清空
 * @property {boolean} [disabled=false] - 是否禁用
 * @property {string} [width] - 组件宽度
 * @property {any} [defaultValue] - 默认值
 */

/**
 * 输入框字段
 * @typedef {BaseField} InputField
 * @property {'input'} type
 */

/**
 * 选择器字段
 * @typedef {BaseField} SelectField
 * @property {'select'} type
 * @property {Array<{label: string, value: any}>} options - 选项列表
 */

/**
 * 日期选择器字段
 * @typedef {BaseField} DateField
 * @property {'date'} type
 */

/**
 * 日期范围选择器字段
 * @typedef {BaseField} DateRangeField
 * @property {'daterange'} type
 */

/**
 * 数字输入框字段
 * @typedef {BaseField} NumberField
 * @property {'number'} type
 * @property {number} [min] - 最小值
 * @property {number} [max] - 最大值
 * @property {number} [step=1] - 步长
 */

/**
 * 级联选择器字段
 * @typedef {BaseField} CascaderField
 * @property {'cascader'} type
 * @property {Array} options - 级联选项
 * @property {Object} [props] - 级联配置
 */

/**
 * 多选框组字段
 * @typedef {BaseField} CheckboxField
 * @property {'checkbox'} type
 * @property {Array<{label: string, value: any}>} options - 选项列表
 */

/**
 * 单选框组字段
 * @typedef {BaseField} RadioField
 * @property {'radio'} type
 * @property {Array<{label: string, value: any}>} options - 选项列表
 */

/**
 * 自定义插槽字段
 * @typedef {BaseField} SlotField
 * @property {'slot'} type
 */

/**
 * 字段类型联合
 * @typedef {InputField|SelectField|DateField|DateRangeField|NumberField|CascaderField|CheckboxField|RadioField|SlotField} SearchFormField
 */

// 常用的选项配置
export const COMMON_OPTIONS = {
  // 状态选项
  STATUS_OPTIONS: [
    { label: '全部', value: '' },
    { label: '有效', value: '1' },
    { label: '无效', value: '0' },
  ],

  // 是否选项
  YES_NO_OPTIONS: [
    { label: '全部', value: '' },
    { label: '是', value: '1' },
    { label: '否', value: '0' },
  ],

  // 公告类型选项
  ANNOUNCEMENT_TYPE_OPTIONS: [
    { label: '全部', value: '' },
    { label: '招标公告', value: 'tender' },
    { label: '中标公告', value: 'award' },
    { label: '变更公告', value: 'change' },
    { label: '废标公告', value: 'cancel' },
  ],

  // 采购方式选项
  PROCUREMENT_METHOD_OPTIONS: [
    { label: '全部', value: '' },
    { label: '公开招标', value: 'open' },
    { label: '邀请招标', value: 'invite' },
    { label: '竞争性谈判', value: 'negotiate' },
    { label: '单一来源', value: 'single' },
    { label: '询价', value: 'inquiry' },
  ],
}

// 字段创建工具函数
export const createField = {
  /**
   * 创建输入框字段
   * @param {string} prop
   * @param {string} label
   * @param {Object} options
   * @returns {InputField}
   */
  input: (prop, label, options = {}) => ({
    type: 'input',
    prop,
    label,
    ...options,
  }),

  /**
   * 创建选择器字段
   * @param {string} prop
   * @param {string} label
   * @param {Array} options
   * @param {Object} config
   * @returns {SelectField}
   */
  select: (prop, label, options, config = {}) => ({
    type: 'select',
    prop,
    label,
    options,
    ...config,
  }),

  /**
   * 创建日期字段
   * @param {string} prop
   * @param {string} label
   * @param {Object} options
   * @returns {DateField}
   */
  date: (prop, label, options = {}) => ({
    type: 'date',
    prop,
    label,
    ...options,
  }),

  /**
   * 创建日期范围字段
   * @param {string} prop
   * @param {string} label
   * @param {Object} options
   * @returns {DateRangeField}
   */
  dateRange: (prop, label, options = {}) => ({
    type: 'daterange',
    prop,
    label,
    ...options,
  }),

  /**
   * 创建数字输入框字段
   * @param {string} prop
   * @param {string} label
   * @param {Object} options
   * @returns {NumberField}
   */
  number: (prop, label, options = {}) => ({
    type: 'number',
    prop,
    label,
    ...options,
  }),
}
