import { inject, onMounted, onUnmounted } from 'vue'

/**
 * 与 AppLayout 分页组件通信的组合式函数
 * @param {Object} options 配置选项
 * @param {number} options.defaultPageSize 默认每页条数
 * @param {Function} options.onPageChange 页面变化回调
 * @param {Function} options.onSizeChange 每页条数变化回调
 */
export function useLayoutPagination(options = {}) {
  const {
    defaultPageSize = 10,
    onPageChange,
    onSizeChange,
  } = options

  // 注入 AppLayout 提供的分页控制
  const pagination = inject('pagination', null)

  if (!pagination) {
    console.warn('useLayoutPagination: 未找到分页上下文，请确保在支持分页的布局中使用')
    return {
      setTotal: () => {},
      getCurrentPage: () => 1,
      getPageSize: () => defaultPageSize,
      reset: () => {},
    }
  }

  // 初始化分页配置
  onMounted(() => {
    pagination.updatePagination({
      total: 0,
      currentPage: 1,
      pageSize: defaultPageSize,
      onPageChange,
      onSizeChange,
    })
  })

  // 清理
  onUnmounted(() => {
    pagination.updatePagination({
      total: 0,
      currentPage: 1,
      pageSize: defaultPageSize,
      onPageChange: null,
      onSizeChange: null,
    })
  })

  // 设置总数
  const setTotal = (total) => {
    pagination.updatePagination({ total })
  }

  // 获取当前页
  const getCurrentPage = () => {
    return pagination.paginationData.currentPage
  }

  // 获取每页条数
  const getPageSize = () => {
    return pagination.paginationData.pageSize
  }

  // 重置分页
  const reset = () => {
    pagination.updatePagination({
      total: 0,
      currentPage: 1,
      pageSize: defaultPageSize,
    })
  }

  // 设置当前页
  const setCurrentPage = (page) => {
    pagination.updatePagination({ currentPage: page })
  }

  // 设置每页条数
  const setPageSize = (size) => {
    pagination.updatePagination({ pageSize: size })
  }

  return {
    setTotal,
    getCurrentPage,
    getPageSize,
    setCurrentPage,
    setPageSize,
    reset,
    // 直接暴露分页数据，方便访问
    paginationData: pagination.paginationData,
  }
}
