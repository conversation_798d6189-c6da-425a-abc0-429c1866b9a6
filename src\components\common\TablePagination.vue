<template>
  <div class="table-pagination">
    <div class="pagination-info">
      <span class="total-info">{{ paginationInfo }}</span>
    </div>
    <div class="pagination-controls">
      <el-config-provider :locale="zhCn">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="pageSizes"
          :total="pagination.total"
          :layout="layout"
          :background="background"
          :small="small"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </el-config-provider>
    </div>
  </div>
</template>

<script setup>
import { watch } from 'vue'
import { usePagination } from '@/composables/usePagination'
import { ElConfigProvider } from 'element-plus'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
defineOptions({
  name: 'TablePagination',
})

// Props
const props = defineProps({
  total: {
    type: Number,
    default: 0,
  },
  defaultPageSize: {
    type: Number,
    default: 10,
  },
  pageSizes: {
    type: Array,
    default: () => [10, 20, 50, 100],
  },
  layout: {
    type: String,
    default: 'sizes, prev, pager, next, jumper',
  },
  background: {
    type: Boolean,
    default: true,
  },
  small: {
    type: Boolean,
    default: false,
  },
})

// Emits
const emit = defineEmits(['page-change', 'size-change'])

// 使用分页组合式函数
const {
  pagination,
  paginationInfo,
  handleCurrentChange,
  handleSizeChange,
  setTotal,
} = usePagination({
  defaultPageSize: props.defaultPageSize,
  pageSizes: props.pageSizes,
  onPageChange: (page, size) => {
    emit('page-change', page, size)
  },
  onSizeChange: (size, page) => {
    emit('size-change', size, page)
  },
})

// 监听 total 变化
watch(
  () => props.total,
  (newTotal) => {
    setTotal(newTotal)
  },
  { immediate: true },
)

// 暴露分页对象给父组件
defineExpose({
  pagination,
  setTotal,
  reset: () => (pagination.currentPage = 1),
})
</script>

<style scoped>
.table-pagination {
  padding: 16px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 56px;
}

.pagination-info {
  flex-shrink: 0;
}

.total-info {
  font-size: 14px;
  color: #666;
  font-weight: 500;
  line-height: 32px;
}

.pagination-controls {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

/* 分页组件样式优化 */
:deep(.el-pagination) {
  .el-pagination__total {
    margin-right: 16px;
    font-weight: 500;
  }

  .el-pagination__sizes {
    margin-right: 16px;
  }

  .el-pagination__jump {
    margin-left: 16px;
  }

  .btn-prev,
  .btn-next {
    border-radius: 4px;
  }

  .el-pager li {
    border-radius: 4px;
    margin: 0 2px;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .table-pagination {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .pagination-info {
    order: 2;
  }

  .pagination-controls {
    order: 1;
    width: 100%;
    justify-content: center;
  }
}
</style>
