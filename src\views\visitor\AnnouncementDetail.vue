<template>
  <div class="announcement-detail">
    <!-- 页面标题和操作按钮 -->
    <div class="page-header">
      <div class="title-section">
        <h1 class="page-title">{{ announcementData.title }}</h1>
        <div class="meta-info">
          <span class="publish-time"
            >发布时间：{{ announcementData.publishTime }}</span
          >
          <span class="deadline"
            >截止日期：{{ announcementData.deadline }}</span
          >
        </div>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="handleComplaint">我要参与</el-button>
      </div>
    </div>

    <!-- 基础信息 -->
    <div class="basic-info-section">
      <div class="section-title">基础信息</div>
      <el-divider />
      <div class="info-grid">
        <div class="info-row">
          <div class="info-item">
            <span class="label">竞价公告编号：</span>
            <span class="value">{{ announcementData.announcementNumber }}</span>
          </div>
          <div class="info-item">
            <span class="label">标题：</span>
            <span class="value">{{ announcementData.projectTitle }}</span>
          </div>
          <div class="info-item">
            <span class="label">采购单位：</span>
            <span class="value">{{ announcementData.purchaser }}</span>
          </div>
        </div>

        <div class="info-row">
          <div class="info-item">
            <span class="label">采购类型：</span>
            <span class="value">{{ announcementData.procurementType }}</span>
          </div>
          <div class="info-item">
            <span class="label">采购方式：</span>
            <span class="value">{{ announcementData.procurementMethod }}</span>
          </div>
          <div class="info-item">
            <span class="label">竞价类型：</span>
            <span class="value">{{ announcementData.bidType }}</span>
          </div>
        </div>

        <div class="info-row">
          <div class="info-item">
            <span class="label">预算方式：</span>
            <span class="value">{{ announcementData.budgetMethod }}</span>
          </div>
          <div class="info-item">
            <span class="label">报名类型：</span>
            <span class="value">{{ announcementData.registrationType }}</span>
          </div>
          <div class="info-item">
            <span class="label">报价次数：</span>
            <span class="value">{{ announcementData.quotationTimes }}</span>
          </div>
        </div>

        <div class="info-row">
          <div class="info-item">
            <span class="label">报名要求：</span>
            <span class="value">{{
              announcementData.registrationRequirement
            }}</span>
          </div>
          <div class="info-item">
            <span class="label">发布类型：</span>
            <span class="value">{{ announcementData.publishType }}</span>
          </div>
          <div class="info-item">
            <span class="label">发布要求：</span>
            <span class="value">{{ announcementData.publishRequirement }}</span>
          </div>
        </div>

        <div class="info-row">
          <div class="info-item">
            <span class="label">报名截止时间：</span>
            <span class="value">{{
              announcementData.registrationDeadline
            }}</span>
          </div>
          <div class="info-item">
            <span class="label">货币：</span>
            <span class="value">{{ announcementData.currency }}</span>
          </div>
          <div class="info-item">
            <span class="label">报价截止时间：</span>
            <span class="value">{{ announcementData.quotationDeadline }}</span>
          </div>
        </div>

        <div class="info-row">
          <div class="info-item">
            <span class="label">结果类型：</span>
            <span class="value">{{ announcementData.resultType }}</span>
          </div>
          <div class="info-item">
            <span class="label">结果期限：</span>
            <span class="value">{{ announcementData.resultDeadline }}</span>
          </div>
          <div class="info-item">
            <span class="label">结果截止时间：</span>
            <span class="value">{{
              announcementData.resultDeadlineSecond
            }}</span>
          </div>
        </div>

        <div class="info-row full-width">
          <div class="info-item">
            <span class="label">公告内容：</span>
            <span class="value">{{ announcementData.content }}</span>
          </div>
        </div>

        <div class="info-row">
          <div class="info-item">
            <span class="label">附件：</span>
            <div class="attachment-links">
              <el-link
                v-for="attachment in announcementData.attachments"
                :key="attachment.id"
                type="primary"
                :href="attachment.url"
                target="_blank"
                class="attachment-link"
              >
                {{ attachment.name }}
              </el-link>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 货物商品信息 -->
    <div class="goods-info-section">
      <h2 class="section-title">货物商品信息</h2>
      <div class="table-container">
        <el-table :data="goodsData">
          <el-table-column
            prop="serialNumber"
            label="序号"
            width="80"
            align="center"
          />
          <el-table-column
            prop="packageNumber"
            label="标包名称"
            width="120"
            align="center"
          />
          <el-table-column
            prop="packageSerial"
            label="标包序号"
            width="120"
            align="center"
          />
          <el-table-column prop="goodsName" label="商品名称" min-width="150" />
          <el-table-column
            prop="manufacturer"
            label="生产厂家"
            width="120"
            align="center"
          />
          <el-table-column
            prop="specifications"
            label="规格"
            width="120"
            align="center"
          />
          <el-table-column
            prop="quantity"
            label="数量"
            width="100"
            align="center"
          />
          <el-table-column prop="unit" label="单位" width="80" align="center" />
          <el-table-column
            prop="budget"
            label="预算上限"
            width="120"
            align="center"
          />
          <el-table-column
            prop="remarks"
            label="备注"
            width="120"
            align="center"
          />
        </el-table>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

defineOptions({
  name: 'AnnouncementDetail',
})

// const route = useRoute()
const router = useRouter()

// 公告详情数据
const announcementData = ref({
  id: '',
  title: 'XXXXXXXXXXXX竞价公告（JJGG202206094587）',
  code: 'JJGG202206094587',
  announcementNumber: '**********',
  projectTitle: 'XXXXXXXXXXXX竞价公告',
  purchaser: 'XXXXXXX医院',
  procurementType: '货物',
  procurementMethod: '竞价',
  bidType: '无需资格审查',
  budgetMethod: '按标包预算',
  registrationType: '人民币',
  quotationTimes: '1次',
  registrationRequirement: '需要资格审查',
  publishType: '2',
  publishRequirement: '满足专业技术要求',
  registrationDeadline: '2022年10月15日',
  currency: '人民币',
  quotationDeadline: '2022年10月30日',
  resultType: '按标包公布结果',
  resultDeadline: '1个月',
  resultDeadlineSecond: '2022年10月30日',
  content:
    '1.竞价公告内容要求及工程概况等详见竞价文件；2.报价时间：020-87338476；2.报价时间内容要求及工程概况等详见竞价文件；3.竞价上限：□，竞价保证金：□，竞价保证金缴纳截止时间：□，竞价保证金缴纳方式：□，竞价保证金退还时间：□，竞价保证金退还方式：□，竞价保证金退还条件：□；4.如因项目取消等原因导致，且未能按时退还竞价保证金的，竞价人可向采购人提出申请。',
  attachments: [
    { id: 1, name: '文件1.pdf', url: '#' },
    { id: 2, name: '文件2.pdf', url: '#' },
  ],
  publishTime: '2022-06-09 13:30',
  deadline: '2022-06-12',
})

// 货物商品信息数据
const goodsData = ref([
  {
    serialNumber: 1,
    packageNumber: '标包1',
    packageSerial: 1,
    goodsName: '注射器口罩',
    manufacturer: '***',
    specifications: '***',
    quantity: 1000,
    unit: '包',
    budget: '***',
    remarks: 'XXXXXXXX',
  },
  {
    serialNumber: 2,
    packageNumber: '标包1',
    packageSerial: 1,
    goodsName: '注射器口罩',
    manufacturer: '***',
    specifications: '***',
    quantity: 1000,
    unit: '包',
    budget: '***',
    remarks: '',
  },
  {
    serialNumber: 3,
    packageNumber: '标包2',
    packageSerial: 2,
    goodsName: '一次性输液器',
    manufacturer: '***',
    specifications: '***',
    quantity: 50,
    unit: '个',
    budget: '***',
    remarks: '',
  },
  {
    serialNumber: 4,
    packageNumber: '标包2',
    packageSerial: 2,
    goodsName: '一次性',
    manufacturer: '***',
    specifications: '***',
    quantity: 100,
    unit: '盒',
    budget: '***',
    remarks: '',
  },
])

// 获取公告详情
const fetchAnnouncementDetail = async () => {
  try {
    // 这里应该调用API获取公告详情
    // const response = await getAnnouncementDetail(id)
    // announcementData.value = response.data
  } catch (error) {
    ElMessage.error(error)
  }
}

// 处理投诉举报
const handleComplaint = () => {
  // ElMessage.info('投诉举报功能待开发')
  router.push('/')
}

onMounted(() => {
  fetchAnnouncementDetail()
})
</script>

<style lang="scss" scoped>
.announcement-detail {
  padding: 0 3%;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 16px 0;
    border-bottom: 1px solid #e8e8e8;

    .title-section {
      flex: 1;
      text-align: center;

      .page-title {
        font-size: 18px;
        font-weight: 600;
        color: #262626;
        margin: 0 0 8px 0;
        line-height: 1.4;
      }

      .meta-info {
        display: flex;
        justify-content: center;
        gap: 120px;
        font-size: 14px;
        color: #8c8c8c;

        .publish-time,
        .deadline {
          white-space: nowrap;
        }
      }
    }
  }

  .basic-info-section,
  .goods-info-section {
    margin-bottom: 32px;

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #262626;
      // margin: 0 0 16px 0;
      // padding-bottom: 8px;
      display: inline-block;
    }
  }

  .info-grid {
    .info-row {
      display: flex;
      margin-bottom: 16px;

      &.full-width {
        .info-item {
          width: 100%;

          .value {
            display: block;
            // margin-top: 8px;
            line-height: 1.6;
            white-space: pre-wrap;
          }
        }
      }

      .info-item {
        flex: 1;
        display: flex;
        align-items: flex-start;
        margin-right: 24px;
        font-size: 14px;

        &:last-child {
          margin-right: 0;
        }

        .label {
          font-weight: 500;
          color: #595959;
          white-space: nowrap;
          margin-right: 8px;
          min-width: 100px;
        }

        .value {
          color: #262626;
          flex: 1;
          word-break: break-all;
        }

        .attachment-links {
          display: flex;
          flex-direction: row;
          gap: 4px;

          .attachment-link {
            font-size: 14px;
          }
        }
      }
    }
  }

  .table-container {
    .el-table {
      font-size: 14px;

      :deep(.el-table__header) {
        th {
          background-color: #fafafa;
          color: #262626;
          font-weight: 600;
        }
      }

      :deep(.el-table__body) {
        td {
          color: #595959;
        }
      }
    }
  }
}

:deep(.el-divider--horizontal) {
  margin: 8px 0 !important;
}
// 响应式设计
@media (max-width: 768px) {
  .announcement-detail {
    padding: 0 3%;

    .page-header {
      flex-direction: column;
      align-items: stretch;

      .header-actions {
        margin-left: 0;
        margin-top: 16px;
        text-align: right;
      }
    }

    .info-grid {
      .info-row {
        flex-direction: column;

        .info-item {
          margin-right: 0;
          margin-bottom: 12px;

          .label {
            min-width: auto;
            margin-bottom: 4px;
          }
        }
      }
    }

    .table-container {
      overflow-x: auto;
    }
  }
}
</style>
