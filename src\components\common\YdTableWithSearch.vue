<template>
  <div class="table-with-search">
    <!-- 搜索表单 -->
    <YdAdvancedSearchForm
      v-if="searchFields && searchFields.length > 0"
      v-model="searchFormData"
      :fields="searchFields"
      :show-collapse-button="showSearchCollapse"
      :collapsed="searchCollapsed"
      :collapsed-rows="searchCollapsedRows"
      :cols-per-row="searchColsPerRow"
      @search="handleSearch"
      @reset="handleReset"
      @update:collapsed="handleSearchCollapseChange"
    >
      <!-- 传递搜索表单的插槽 -->
      <template v-for="(_, slotName) in $slots" :key="slotName" #[slotName]="slotProps">
        <slot v-if="slotName.startsWith('search-')" :name="slotName" v-bind="slotProps" />
      </template>
    </YdAdvancedSearchForm>

    <!-- 表格容器 -->
    <div class="table-container" :style="tableContainerStyle">
      <YdDataTable
        v-bind="$attrs"
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :show-pagination="showPagination"
        :total="total"
        :current-page="currentPage"
        :page-size="pageSize"
        :page-sizes="pageSizes"
        :table-height="tableHeight"
        v-on="tableEvents"
      >
        <!-- 传递表格的插槽 -->
        <template v-for="(_, slotName) in $slots" :key="slotName" #[slotName]="slotProps">
          <slot v-if="!slotName.startsWith('search-')" :name="slotName" v-bind="slotProps" />
        </template>
      </YdDataTable>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import YdAdvancedSearchForm from './YdAdvancedSearchForm.vue'
import YdDataTable from './YdDataTable.vue'

defineOptions({
  name: 'YdTableWithSearch',
  inheritAttrs: false,
})

// Props
const props = defineProps({
  // 表格列配置
  columns: {
    type: Array,
    required: true,
  },
  // 表格数据
  data: {
    type: Array,
    default: () => [],
  },
  // 加载状态
  loading: {
    type: Boolean,
    default: false,
  },
  // 搜索字段配置
  searchFields: {
    type: Array,
    default: () => [],
  },
  // 搜索表单数据
  searchData: {
    type: Object,
    default: () => ({}),
  },
  // 是否显示搜索展开收起
  showSearchCollapse: {
    type: Boolean,
    default: true,
  },
  // 搜索表单默认收起状态
  searchCollapsed: {
    type: Boolean,
    default: true,
  },
  // 搜索表单收起时显示行数
  searchCollapsedRows: {
    type: Number,
    default: 1,
  },
  // 搜索表单每行列数
  searchColsPerRow: {
    type: Number,
    default: 3,
  },
  // 分页相关
  showPagination: {
    type: Boolean,
    default: true,
  },
  total: {
    type: Number,
    default: 0,
  },
  currentPage: {
    type: Number,
    default: 1,
  },
  pageSize: {
    type: Number,
    default: 10,
  },
  pageSizes: {
    type: Array,
    default: () => [10, 20, 50, 100],
  },
  // 表格高度策略
  heightStrategy: {
    type: String,
    default: 'auto', // 'auto' | 'fixed' | 'viewport'
    validator: (value) => ['auto', 'fixed', 'viewport'].includes(value),
  },
  // 固定高度值（当 heightStrategy 为 'fixed' 时使用）
  fixedHeight: {
    type: [String, Number],
    default: 600,
  },
})

// Emits
const emit = defineEmits([
  'search',
  'reset',
  'update:searchData',
  'update:currentPage',
  'update:pageSize',
  'page-change',
  'size-change',
  'selection-change',
  'row-click',
  'sort-change',
])

// 搜索表单数据
const searchFormData = reactive({})

// 计算表格数据
const tableData = computed(() => {
  return props.data
})

// 计算表格容器样式
const tableContainerStyle = computed(() => {
  const styles = {}
  
  switch (props.heightStrategy) {
    case 'fixed':
      styles.height = typeof props.fixedHeight === 'number' 
        ? `${props.fixedHeight}px` 
        : props.fixedHeight
      break
    case 'viewport':
      // 计算视口高度减去搜索表单和其他元素的高度
      const searchFormHeight = props.searchFields?.length > 0 ? 120 : 0
      const headerHeight = 60 // 假设页面头部高度
      const paddingHeight = 40 // 页面内边距
      styles.height = `calc(100vh - ${headerHeight + searchFormHeight + paddingHeight}px)`
      break
    case 'auto':
    default:
      styles.minHeight = '400px'
      styles.height = 'auto'
      break
  }
  
  return styles
})

// 计算表格高度
const tableHeight = computed(() => {
  if (props.heightStrategy === 'auto') {
    return undefined
  }
  return '100%'
})

// 表格事件处理
const tableEvents = computed(() => {
  return {
    'selection-change': (selection) => emit('selection-change', selection),
    'row-click': (row, column, event) => emit('row-click', row, column, event),
    'sort-change': (sortInfo) => emit('sort-change', sortInfo),
    'page-change': (page, size) => {
      emit('update:currentPage', page)
      emit('page-change', page, size)
    },
    'size-change': (size, page) => {
      emit('update:pageSize', size)
      emit('update:currentPage', page)
      emit('size-change', size, page)
    },
  }
})

// 搜索处理
const handleSearch = (formData) => {
  emit('search', formData)
}

// 重置处理
const handleReset = (formData) => {
  emit('reset', formData)
}

// 搜索展开收起状态变化
const handleSearchCollapseChange = (collapsed) => {
  // 可以在这里添加额外的逻辑
}

// 初始化搜索表单数据
const initSearchFormData = () => {
  Object.keys(searchFormData).forEach(key => {
    delete searchFormData[key]
  })
  Object.assign(searchFormData, props.searchData)
}

// 监听搜索数据变化
watch(() => props.searchData, initSearchFormData, { immediate: true, deep: true })

// 监听搜索表单数据变化
watch(
  searchFormData,
  (newVal) => {
    emit('update:searchData', { ...newVal })
  },
  { deep: true }
)

// 暴露方法
defineExpose({
  searchFormData,
  handleSearch,
  handleReset,
})
</script>

<style scoped>
.table-with-search {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 0;
}

.table-container {
  flex: 1;
  min-height: 0;
  overflow: hidden;
}

/* 确保表格容器能够正确处理高度 */
.table-container :deep(.data-table-container) {
  height: 100%;
}
</style>
