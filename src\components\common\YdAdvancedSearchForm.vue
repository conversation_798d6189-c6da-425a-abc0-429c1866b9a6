<template>
  <div class="advanced-search-form">
    <el-form
      ref="formRef"
      :model="formData"
      :label-width="labelWidth"
      class="search-form-content"
    >
      <!-- 使用 Grid 布局的表单字段容器 -->
      <div
        ref="fieldsWrapperRef"
        :class="[
          'form-fields-grid',
          `grid-cols-${colsPerRow}`,
          { collapsed: isCollapsed && showCollapseButton },
        ]"
      >
        <!-- 表单字段 -->
        <div
          v-for="(field, index) in fields"
          :key="field.name"
          :class="[
            'form-field-item',
            {
              'hidden': shouldHideField(index),
              'flex-shrink-0': true,
            },
          ]"
        >
          <el-form-item
            :label="field.label"
            :prop="field.name"
            :required="field.required"
            class="form-item"
          >
            <!-- 文本输入框 -->
            <el-input
              v-if="field.type === 'text'"
              v-model="formData[field.name]"
              :placeholder="field.placeholder || `请输入${field.label}`"
              :clearable="field.clearable !== false"
              :disabled="field.disabled"
            />

            <!-- 选择器 -->
            <el-select
              v-else-if="field.type === 'select'"
              v-model="formData[field.name]"
              :placeholder="field.placeholder || `请选择${field.label}`"
              :clearable="field.clearable !== false"
              :disabled="field.disabled"
              style="width: 100%"
            >
              <el-option
                v-for="option in field.options"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>

            <!-- 日期选择器 -->
            <el-date-picker
              v-else-if="field.type === 'date'"
              v-model="formData[field.name]"
              type="date"
              :placeholder="field.placeholder || `请选择${field.label}`"
              :clearable="field.clearable !== false"
              :disabled="field.disabled"
              style="width: 100%"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />

            <!-- 日期范围选择器 -->
            <el-date-picker
              v-else-if="field.type === 'daterange'"
              v-model="formData[field.name]"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :clearable="field.clearable !== false"
              :disabled="field.disabled"
              style="width: 100%"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />

            <!-- 数字输入框 -->
            <el-input-number
              v-else-if="field.type === 'number'"
              v-model="formData[field.name]"
              :placeholder="field.placeholder || `请输入${field.label}`"
              :disabled="field.disabled"
              :min="field.min"
              :max="field.max"
              :step="field.step || 1"
              style="width: 100%"
            />

            <!-- 自定义插槽 -->
            <slot
              v-else-if="field.type === 'slot'"
              :name="field.name"
              :field="field"
              :value="formData[field.name]"
              :set-value="(val) => (formData[field.name] = val)"
            />

            <!-- 默认文本输入框 -->
            <el-input
              v-else
              v-model="formData[field.name]"
              :placeholder="field.placeholder || `请输入${field.label}`"
              :clearable="field.clearable !== false"
              :disabled="field.disabled"
            />
          </el-form-item>
        </div>
        
        <!-- 操作按钮区域 -->
        <div v-if="showActions" class="form-actions">
          <div class="action-buttons">
            <el-button
              type="primary"
              class="search-btn"
              @click="handleSearch"
            >
              {{ searchText }}
            </el-button>
            <el-button class="reset-btn" @click="handleReset">
              {{ resetText }}
            </el-button>
            <!-- 展开/收起按钮 -->
            <el-button
              v-if="showCollapseButton"
              type="text"
              class="collapse-btn"
              @click="toggleCollapse"
            >
              <el-icon class="collapse-icon">
                <ArrowUp v-if="isCollapsed" />
                <ArrowDown v-else />
              </el-icon>
              {{ isCollapsed ? '展开' : '收起' }}
            </el-button>
          </div>
        </div>
      </div>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, watch, computed, nextTick, onMounted } from 'vue'
import { ArrowUp, ArrowDown } from '@element-plus/icons-vue'
import { useElementVisibility } from '@vueuse/core'

defineOptions({
  name: 'YdAdvancedSearchForm',
})

// Props
const props = defineProps({
  // 表单字段配置数组
  fields: {
    type: Array,
    required: true,
  },
  // 表单数据
  modelValue: {
    type: Object,
    default: () => ({}),
  },
  // 标签宽度
  labelWidth: {
    type: String,
    default: '100px',
  },
  // 是否显示操作按钮
  showActions: {
    type: Boolean,
    default: true,
  },
  // 搜索按钮文本
  searchText: {
    type: String,
    default: '查询',
  },
  // 重置按钮文本
  resetText: {
    type: String,
    default: '重置',
  },
  // 是否显示展开收起按钮
  showCollapseButton: {
    type: Boolean,
    default: true,
  },
  // 默认收起状态
  collapsed: {
    type: Boolean,
    default: true,
  },
  // 收起时显示的行数
  collapsedRows: {
    type: Number,
    default: 1,
  },
  // 每行显示的列数
  colsPerRow: {
    type: Number,
    default: 3,
  },
})

// Emits
const emit = defineEmits(['update:modelValue', 'search', 'reset', 'update:collapsed'])

// 表单引用
const formRef = ref()
const fieldsWrapperRef = ref()

// 表单数据
const formData = reactive({})

// 展开收起状态
const isCollapsed = ref(props.collapsed)

// 行映射 - 记录每行有多少个元素
const rowMapping = ref({})
const isCalculated = ref(false)

// 元素可见性检测
const isVisible = useElementVisibility(fieldsWrapperRef)

// 计算需要保留的表单项索引
const keepFormItemIndex = computed(() => {
  const rows = props.collapsedRows
  const mapping = rowMapping.value
  let maxItem = 0
  for (let index = 1; index <= rows; index++) {
    maxItem += mapping?.[index] ?? 0
  }
  return maxItem - 1 || 1
})

// 判断是否应该隐藏字段
const shouldHideField = (index) => {
  if (!props.showCollapseButton || !isCollapsed.value || !isCalculated.value) {
    return false
  }
  const keepIndex = keepFormItemIndex.value
  return keepIndex <= index
}

// 动态计算行映射
const calculateRowMapping = async () => {
  if (!props.showCollapseButton) {
    return
  }

  await nextTick()
  if (!fieldsWrapperRef.value) {
    return
  }

  const formItems = [...fieldsWrapperRef.value.children]
  const container = fieldsWrapperRef.value
  const containerStyles = window.getComputedStyle(container)
  const rowHeights = containerStyles
    .getPropertyValue('grid-template-rows')
    .split(' ')

  const containerRect = container?.getBoundingClientRect()

  rowMapping.value = {}

  formItems.forEach((el) => {
    const itemRect = el.getBoundingClientRect()

    // 计算元素在第几行
    const itemTop = itemRect.top - containerRect.top
    let rowStart = 0
    let cumulativeHeight = 0

    for (const [i, rowHeight] of rowHeights.entries()) {
      cumulativeHeight += Number.parseFloat(rowHeight)
      if (itemTop < cumulativeHeight) {
        rowStart = i + 1
        break
      }
    }

    if (rowStart > props.collapsedRows) {
      return
    }

    rowMapping.value[rowStart] = (rowMapping.value[rowStart] ?? 0) + 1
    isCalculated.value = true
  })
}

// 监听变化重新计算
watch(
  [
    () => props.showCollapseButton,
    () => props.fields?.length,
    () => isVisible.value,
  ],
  async ([val]) => {
    if (val) {
      await nextTick()
      rowMapping.value = {}
      isCalculated.value = false
      await calculateRowMapping()
    }
  },
)

// 初始化表单数据
const initFormData = () => {
  // 清空现有数据
  Object.keys(formData).forEach((key) => {
    delete formData[key]
  })

  // 设置默认值
  props.fields.forEach((field) => {
    if (field.defaultValue !== undefined) {
      formData[field.name] = field.defaultValue
    } else {
      // 根据类型设置默认值
      switch (field.type) {
        case 'checkbox':
          formData[field.name] = []
          break
        case 'daterange':
          formData[field.name] = []
          break
        default:
          formData[field.name] = ''
      }
    }
  })

  // 合并传入的初始值
  Object.assign(formData, props.modelValue)
}

// 展开收起切换
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value
  emit('update:collapsed', isCollapsed.value)
}

// 搜索
const handleSearch = () => {
  emit('search', { ...formData })
}

// 重置
const handleReset = () => {
  formRef.value?.resetFields()
  initFormData()
  emit('reset', { ...formData })
}

// 监听字段变化，重新初始化
watch(() => props.fields, initFormData, { immediate: true, deep: true })

// 监听外部数据变化
watch(
  () => props.modelValue,
  (newVal) => {
    Object.assign(formData, newVal)
  },
  { deep: true },
)

// 监听内部数据变化，向外发送
watch(
  formData,
  (newVal) => {
    emit('update:modelValue', { ...newVal })
  },
  { deep: true },
)

// 监听collapsed prop变化
watch(
  () => props.collapsed,
  (newVal) => {
    isCollapsed.value = newVal
  },
)

// 组件挂载后计算行映射
onMounted(() => {
  calculateRowMapping()
})

// 暴露方法
defineExpose({
  formRef,
  formData,
  handleSearch,
  handleReset,
  toggleCollapse,
})
</script>

<style scoped>
.advanced-search-form {
  background: #fff;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
  margin-bottom: 16px;
}

.search-form-content {
  padding: 20px;
}

.form-fields-grid {
  display: grid;
  gap: 16px 20px;
  align-items: start;
}

.grid-cols-1 {
  grid-template-columns: 1fr;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-cols-3 {
  grid-template-columns: repeat(3, 1fr);
}

.grid-cols-4 {
  grid-template-columns: repeat(4, 1fr);
}

.form-field-item {
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.form-field-item.hidden {
  display: none;
}

.form-item {
  margin-bottom: 0;
}

.form-actions {
  grid-column: -2 / -1;
  margin-left: auto;
  display: flex;
  align-items: flex-start;
  justify-content: flex-end;
}

.action-buttons {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.search-btn {
  min-width: 80px;
}

.reset-btn {
  min-width: 80px;
}

.collapse-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  color: #1890ff;
  border: none;
  background: none;
  cursor: pointer;
  font-size: 14px;
}

.collapse-btn:hover {
  color: #40a9ff;
  background: #f0f8ff;
}

.collapse-icon {
  font-size: 12px;
  transition: transform 0.3s ease;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .grid-cols-4 {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 992px) {
  .grid-cols-3,
  .grid-cols-4 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .grid-cols-2,
  .grid-cols-3,
  .grid-cols-4 {
    grid-template-columns: 1fr;
  }

  .form-actions {
    grid-column: 1 / -1;
    margin-left: 0;
    justify-content: center;
  }

  .action-buttons {
    justify-content: center;
    width: 100%;
  }
}

/* Element Plus 样式覆盖 */
:deep(.el-form-item__label) {
  font-weight: 500;
  color: #333;
  line-height: 32px;
}

:deep(.el-form-item__content) {
  line-height: 32px;
}

:deep(.el-input),
:deep(.el-select),
:deep(.el-date-editor),
:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-button) {
  border-radius: 4px;
}
</style>
