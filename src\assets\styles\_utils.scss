@use 'sass:list';

// ---------- 1. 基础变量 ----------
$prefix: '';                 // 如需命名空间可改为 'ui-'
$breakpoints: (
  'sm': 576px,
  'md': 768px,
  'lg': 992px,
);
$spacers: (0, 4px, 8px, 12px, 16px, 20px, 24px, 32px, 48px, 64px);
$font-sizes: (12, 14, 16, 18, 20, 24, 32);
$radii: (2px, 4px, 6px, 8px, 50%);
$z-indexes: (10, 20, 30, 40, 50);

// ---------- 2. 颜色 ----------
:root {
  --primary: #1677ff;
  --danger : #ff4d4f;
  --text   : #262626;
  --bg     : #ffffff;
}

/* ---------- 灰色变量 ---------- */
$grays: (
  0: #ffffff,
  1: #fafafa,
  2: #f5f5f5,
  3: #e6e6e6,
  4: #d1d1d1,
  5: #b3b3b3,
  6: #8c8c8c,
  7: #595959,
  8: #262626,
  9: #000000,
);

/* 注入 CSS 变量 */
:root {
  @each $level, $color in $grays {
    --gray-#{$level}: #{$color};
  }
}

/* ---------- 工具类 ---------- */
@each $level, $color in $grays {
  .text-gray-#{$level}  { color: var(--gray-#{$level}) !important; }
  .bg-gray-#{$level}    { background-color: var(--gray-#{$level}) !important; }
  .border-gray-#{$level} { border-color: var(--gray-#{$level}) !important; }

  /* hover 状态（可选） */
  .hover\:bg-gray-#{$level}:hover { background-color: var(--gray-#{$level}) !important; }
  .hover\:text-gray-#{$level}:hover { color: var(--gray-#{$level}) !important; }
}

// 工具函数：拼 class 名
@function cls($name) {
  @return #{$prefix}#{$name};
}

// 1️⃣ display 相关
.#{$prefix}flex { display: flex !important; }
.#{$prefix}grid { display: grid !important; }
.#{$prefix}block { display: block !important; }
.#{$prefix}inline-block { display: inline-block !important; }

// 2️⃣ flex 快捷
.#{$prefix}flex-center {
  display: flex !important;
  align-items: center;
  justify-content: center;
}
.#{$prefix}flex-between {
  display: flex !important;
  align-items: center;
  justify-content: space-between;
}
.#{$prefix}flex-col { flex-direction: column !important; }

// 3️⃣ 间距 (m / p / gap)
@each $val in $spacers {
  $i: list.index($spacers, $val);

  .#{cls('m-') + $i}  { margin: $val !important; }
  .#{cls('mt-') + $i} { margin-top: $val !important; }
  .#{cls('mr-') + $i} { margin-right: $val !important; }
  .#{cls('mb-') + $i} { margin-bottom: $val !important; }
  .#{cls('ml-') + $i} { margin-left: $val !important; }
  .#{cls('p-') + $i}  { padding: $val !important; }
  .#{cls('pt-') + $i} { padding-top: $val !important; }
  .#{cls('pr-') + $i} { padding-right: $val !important; }
  .#{cls('pb-') + $i} { padding-bottom: $val !important; }
  .#{cls('pl-') + $i} { padding-left: $val !important; }
  .#{cls('gap-') + $i} { gap: $val !important; }
}

// 4️⃣ 字体大小
@each $fz in $font-sizes {
  .#{cls('text-') + $fz} { font-size: #{$fz}px !important; }
}

/* ---------- font-weight（Tailwind 风格） ---------- */
.font-light   { font-weight: 300 !important; }
.font-normal  { font-weight: 400 !important; }
.font-medium  { font-weight: 500 !important; }
.font-semibold{ font-weight: 600 !important; }
.font-bold    { font-weight: 700 !important; }


// 5️⃣ 圆角
@each $r in $radii {
  $i: list.index($radii, $r);
  .#{cls('rounded-') + $i} { border-radius: $r !important; }
}
.#{$prefix}rounded-full { border-radius: 50% !important; }

// 6️⃣ z-index
@each $z in $z-indexes {
  $i: list.index($z-indexes, $z);
  .#{cls('z-') + $i} { z-index: $z !important; }
}

// 7️⃣ 颜色 / 背景
.#{$prefix}text-primary { color: var(--primary) !important; }
.#{$prefix}text-danger  { color: var(--danger) !important; }
.#{$prefix}bg-primary   { background-color: var(--primary) !important; }
.#{$prefix}bg-danger    { background-color: var(--danger) !important; }

// 8️⃣ 阴影
.#{$prefix}shadow {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
}
.#{$prefix}shadow-lg {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.25) !important;
}

// 9️⃣ 光标 & 过渡
.#{$prefix}pointer   { cursor: pointer !important; }
.#{$prefix}transition { transition: all 0.3s ease !important; }

// 🔟 位置
.#{$prefix}relative { position: relative !important; }
.#{$prefix}absolute { position: absolute !important; }
.#{$prefix}fixed    { position: fixed !important; }
.#{$prefix}sticky   { position: sticky !important; }
