<template>
  <div class="vben-page-container">
    <!-- 页面头部 -->
    <div
      v-if="
        description ||
        $slots.description ||
        title ||
        $slots.title ||
        $slots.extra
      "
      ref="headerRef"
      class="vben-page-header"
    >
      <div class="vben-page-header-content">
        <slot name="title">
          <div v-if="title" class="vben-page-title">
            {{ title }}
          </div>
        </slot>

        <slot name="description">
          <p v-if="description" class="vben-page-description">
            {{ description }}
          </p>
        </slot>
      </div>

      <div v-if="$slots.extra">
        <slot name="extra"></slot>
      </div>
    </div>

    <!-- 页面内容 -->
    <div class="vben-page-content" :style="contentStyle">
      <slot></slot>
    </div>

    <!-- 页面底部 -->
    <div v-if="$slots.footer" ref="footerRef" class="vben-page-footer">
      <slot name="footer"></slot>
    </div>
  </div>
</template>

<script setup>
import { computed, nextTick, onMounted, ref } from 'vue'
import { CSS_VARIABLE_LAYOUT_CONTENT_HEIGHT } from '@/constants/globals'

defineOptions({
  name: 'VbenPageContainer',
})

const props = defineProps({
  title: {
    type: String,
    default: '',
  },
  description: {
    type: String,
    default: '',
  },
  contentClass: {
    type: String,
    default: '',
  },
  /**
   * 根据content可见高度自适应
   */
  autoContentHeight: {
    type: Boolean,
    default: false,
  },
  headerClass: {
    type: String,
    default: '',
  },
  footerClass: {
    type: String,
    default: '',
  },
  /**
   * 自定义高度偏移量（像素）
   * @default 0
   */
  heightOffset: {
    type: Number,
    default: 0,
  },
})

const headerHeight = ref(0)
const footerHeight = ref(0)
const shouldAutoHeight = ref(false)

const headerRef = ref(null)
const footerRef = ref(null)

const contentStyle = computed(() => {
  if (props.autoContentHeight) {
    const finalHeight = `calc(var(${CSS_VARIABLE_LAYOUT_CONTENT_HEIGHT}) - ${headerHeight.value}px - ${footerHeight.value}px - ${props.heightOffset}px)`
    // eslint-disable-next-line no-console
    console.log('📦 VbenPageContainer - contentStyle:', {
      autoContentHeight: props.autoContentHeight,
      cssVariable: CSS_VARIABLE_LAYOUT_CONTENT_HEIGHT,
      headerHeight: headerHeight.value,
      footerHeight: footerHeight.value,
      heightOffset: props.heightOffset,
      finalHeight,
      shouldAutoHeight: shouldAutoHeight.value,
    })
    return {
      height: finalHeight,
      overflowY: shouldAutoHeight.value ? 'auto' : 'unset',
    }
  }
  return {}
})

async function calcContentHeight() {
  if (!props.autoContentHeight) {
    // eslint-disable-next-line no-console
    console.log('📦 VbenPageContainer - 未启用自动高度')
    return
  }
  
  // eslint-disable-next-line no-console
  console.log('📦 VbenPageContainer - 开始计算内容高度')
  
  await nextTick()
  
  const oldHeaderHeight = headerHeight.value
  const oldFooterHeight = footerHeight.value
  
  headerHeight.value = headerRef.value?.offsetHeight || 0
  footerHeight.value = footerRef.value?.offsetHeight || 0
  
  // 获取CSS变量的值
  const contentHeightVar = getComputedStyle(
    document.documentElement,
  ).getPropertyValue(CSS_VARIABLE_LAYOUT_CONTENT_HEIGHT)
  
  // eslint-disable-next-line no-console
  console.log('📦 VbenPageContainer - 高度计算结果:', {
    headerRef: headerRef.value,
    footerRef: footerRef.value,
    oldHeaderHeight,
    oldFooterHeight,
    newHeaderHeight: headerHeight.value,
    newFooterHeight: footerHeight.value,
    contentHeightVar,
    heightOffset: props.heightOffset,
  })
  
  // 延迟启用自动高度，避免闪烁
  setTimeout(() => {
    shouldAutoHeight.value = true
    // eslint-disable-next-line no-console
    console.log('📦 VbenPageContainer - 启用自动高度完成')
  }, 30)
}

onMounted(() => {
  calcContentHeight()
})
</script>

<style scoped>
.vben-page-container {
  position: relative;
  height: 100%;
}

.vben-page-header {
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: flex-end;
  padding: 16px 24px;
  position: relative;
}

.vben-page-header-content {
  flex: 1;
}

.vben-page-title {
  margin-bottom: 8px;
  font-size: 18px;
  font-weight: 600;
  line-height: 1.5;
  color: #1f2937;
}

.vben-page-description {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
}

.vben-page-content {
  padding: 0;
  background: transparent;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.vben-page-footer {
  background: #fff;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  padding: 16px 24px;
  border-top: 1px solid #f0f0f0;
}
</style>
