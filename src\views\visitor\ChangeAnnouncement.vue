<template>
  <div class="change-announcement-page">
    <!-- 搜索表单 -->
    <YdSearchForm
      v-model="searchForm"
      :fields="searchFields"
      @search="handleSearch"
      @reset="handleReset"
    />

    <!-- 数据表格 -->
    <div class="data-table-wrapper">
      <YdDataTable
        :show-selection="true"
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :total="total"
        :current-page="currentPage"
        :page-size="pageSize"
        highlight-current-row
        :row-clickable="true"
        @page-change="handlePageChange"
        @size-change="handleSizeChange"
        @sort-change="handleSortChange"
        @row-click="handleRowClick"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import YdSearchForm from '@/components/common/YdSearchForm.vue'
import YdDataTable from '@/components/common/YdDataTable.vue'
import { ElMessage } from 'element-plus'

defineOptions({
  name: 'ChangeAnnouncement',
})

// 搜索表单数据
const searchForm = ref({})

// 搜索字段配置
const searchFields = [
  {
    type: 'text',
    name: 'announcementNumber',
    label: '公告编号',
    placeholder: '请输入公告编号',
  },
  {
    type: 'text',
    name: 'title',
    label: '公告标题',
    placeholder: '请输入公告标题',
  },
  {
    type: 'daterange',
    name: 'publishTime',
    label: '发布时间',
  },
  {
    type: 'select',
    name: 'procurementMethod',
    label: '采购方式',
    placeholder: '请选择采购方式',
    options: [
      { label: '全部', value: '' },
      { label: '货物', value: 'goods' },
      { label: '服务', value: 'service' },
      { label: '工程', value: 'project' },
    ],
  },
  {
    type: 'select',
    name: 'status',
    label: '状态',
    placeholder: '请选择状态',
    options: [
      { label: '全部', value: '' },
      { label: '正在报名', value: 'registering' },
      { label: '已截止', value: 'expired' },
      { label: '已暂停', value: 'paused' },
    ],
  },
]

// 表格列配置
const columns = [
  { dataIndex: 'serialNumber', title: '序号', width: 80 },
  { dataIndex: 'announcementNumber', title: '公告编号', width: 120 },
  { dataIndex: 'title', title: '公告标题', minWidth: 200 },
  { dataIndex: 'purchaser', title: '采购单位', width: 150 },
  { dataIndex: 'type', title: '采购类型', width: 100 },
  {
    dataIndex: 'publishTime',
    title: '发布时间',
    width: 150,
    sortable: 'custom',
  },
  {
    dataIndex: 'registrationDeadline',
    title: '报名截止时间',
    width: 150,
    sortable: 'custom',
  },
  { dataIndex: 'status', title: '状态', width: 80 },
]

// 表格数据
const tableData = ref([])
const loading = ref(false)
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)

// 排序状态
const sortInfo = ref({
  prop: '',
  order: '',
})

// 路由实例
const router = useRouter()

// 模拟数据
const mockData = [
  {
    id: 1,
    serialNumber: 1,
    announcementNumber: '**********',
    title: '康复治疗中心',
    purchaser: 'XXXX医疗机构',
    type: '货物',
    publishTime: '2022-06-07 15:34:09',
    registrationDeadline: '2022-11-07',
    status: '正在报名',
  },
  {
    id: 2,
    serialNumber: 2,
    announcementNumber: '**********',
    title: '巴马瑶族自治县医疗设备治疗仪采购',
    purchaser: 'XXXXX民医院',
    type: '货物',
    publishTime: '2022-06-07 15:34:09',
    registrationDeadline: '2022-11-07',
    status: '正在报名',
  },
  {
    id: 3,
    serialNumber: 3,
    announcementNumber: '**********',
    title: '康复治疗中心设备采购变更公告',
    purchaser: 'XXXX医疗机构',
    type: '货物',
    publishTime: '2022-06-07 15:34:09',
    registrationDeadline: '2022-11-07',
    status: '正在报名',
  },
  {
    id: 4,
    serialNumber: 4,
    announcementNumber: '**********',
    title: '分析检查子平台16012022324',
    purchaser: 'XXXXX民医院',
    type: '服务',
    publishTime: '2022-06-07 15:34:09',
    registrationDeadline: '2022-10-30',
    status: '正在报名',
  },
  {
    id: 5,
    serialNumber: 5,
    announcementNumber: '**********',
    title: '学习与康复设备采购子科室物品...',
    purchaser: 'XXXX医疗机构',
    type: '货物',
    publishTime: '2022-06-07 15:34:09',
    registrationDeadline: '2022-11-07',
    status: '正在报名',
  },
  {
    id: 6,
    serialNumber: 6,
    announcementNumber: '**********',
    title: '康复治疗中心',
    purchaser: 'XXXXX民医院',
    type: '货物',
    publishTime: '2022-06-07 15:34:09',
    registrationDeadline: '2022-11-07',
    status: '正在报名',
  },
  {
    id: 7,
    serialNumber: 7,
    announcementNumber: '**********',
    title: '巴马瑶族自治县医疗设备治疗仪采购',
    purchaser: 'XXXX医疗机构',
    type: '货物',
    publishTime: '2022-06-07 15:34:09',
    registrationDeadline: '2022-11-07',
    status: '正在报名',
  },
  {
    id: 8,
    serialNumber: 8,
    announcementNumber: '**********',
    title: '康复治疗中心设备采购变更公告',
    purchaser: 'XXXXX民医院',
    type: '货物',
    publishTime: '2022-06-07 15:34:09',
    registrationDeadline: '2022-12-07',
    status: '正在报名',
  },
  {
    id: 9,
    serialNumber: 9,
    announcementNumber: '**********',
    title: '分析检查子平台16012022324',
    purchaser: 'XXXX医疗机构',
    type: '服务',
    publishTime: '2022-06-07 15:34:09',
    registrationDeadline: '2022-07-07',
    status: '报名截止',
  },
  {
    id: 10,
    serialNumber: 10,
    announcementNumber: '**********',
    title: '学习与康复设备采购子科室物品...',
    purchaser: 'XXXXX民医院',
    type: '货物',
    publishTime: '2022-06-07 15:34:09',
    registrationDeadline: '2022-07-07',
    status: '报名截止',
  },
  {
    id: 10,
    serialNumber: 10,
    announcementNumber: '**********',
    title: '学习与康复设备采购子科室物品...',
    purchaser: 'XXXXX民医院',
    type: '货物',
    publishTime: '2022-06-07 15:34:09',
    registrationDeadline: '2022-07-07',
    status: '报名截止',
  },
  {
    id: 10,
    serialNumber: 10,
    announcementNumber: '**********',
    title: '学习与康复设备采购子科室物品...',
    purchaser: 'XXXXX民医院',
    type: '货物',
    publishTime: '2022-06-07 15:34:09',
    registrationDeadline: '2022-07-07',
    status: '报名截止',
  },
  {
    id: 10,
    serialNumber: 10,
    announcementNumber: '**********',
    title: '学习与康复设备采购子科室物品...',
    purchaser: 'XXXXX民医院',
    type: '货物',
    publishTime: '2022-06-07 15:34:09',
    registrationDeadline: '2022-07-07',
    status: '报名截止',
  },
]

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  loadData()
}

// 重置
const handleReset = () => {
  currentPage.value = 1
  loadData()
}

// 分页变化
const handlePageChange = (page) => {
  currentPage.value = page
  loadData()
}

// 页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  loadData()
}

// 排序变化
const handleSortChange = ({ prop, order }) => {
  sortInfo.value.prop = prop
  sortInfo.value.order = order
  currentPage.value = 1
  loadData()
}

// 行点击
const handleRowClick = (row) => {
  if (row && row.id) {
    router.push(`/announcement/${row.id}`)
  }
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 500))

    let data = [...mockData]

    // 应用排序
    if (sortInfo.value.prop && sortInfo.value.order) {
      data.sort((a, b) => {
        const aValue = a[sortInfo.value.prop]
        const bValue = b[sortInfo.value.prop]

        // 日期排序
        if (
          sortInfo.value.prop === 'publishTime' ||
          sortInfo.value.prop === 'registrationDeadline'
        ) {
          const dateA = new Date(aValue)
          const dateB = new Date(bValue)

          if (sortInfo.value.order === 'ascending') {
            return dateA - dateB
          } else {
            return dateB - dateA
          }
        }

        // 其他类型排序
        if (sortInfo.value.order === 'ascending') {
          return aValue > bValue ? 1 : -1
        } else {
          return aValue < bValue ? 1 : -1
        }
      })
    }

    tableData.value = data
    total.value = data.length
  } catch (error) {
    ElMessage.error(error)
  } finally {
    loading.value = false
  }
}

// 初始化
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.change-announcement-page {
  /* 使用flex布局，让容器占满父元素 */
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
  box-sizing: border-box;
  overflow: hidden;
  min-height: 0;
  /* 设置页面最大高度，防止溢出 */
  max-height: 75vh;
}

.data-table-wrapper {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  /* 设置表格区域最大高度 */
  max-height: calc(100vh - 200px); /* 减去搜索表单和其他元素的高度 */
}

/* 确保YdDataTable组件占满剩余空间 */
.data-table-wrapper :deep(.data-table-container) {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  max-height: 100%;
}

/* 表格主体区域可滚动 */
.data-table-wrapper :deep(.table-wrapper) {
  flex: 1;
  min-height: 0;
  overflow: hidden;
  max-height: calc(100% - 60px); /* 减去分页器的高度 */
}

.data-table-wrapper :deep(.el-table) {
  height: 100%;
  max-height: 100%;
}

.data-table-wrapper :deep(.el-table .el-table__body-wrapper) {
  max-height: none !important;
  overflow-y: auto;
  /* 设置表格内容区域的最大高度 */
  max-height: calc(100vh - 280px) !important;
}

.data-table-wrapper :deep(.el-table .el-table__header-wrapper) {
  overflow: hidden;
}

/* 分页区域固定在底部 */
.data-table-wrapper :deep(.table-pagination) {
  flex-shrink: 0;
  border-top: 1px solid #f0f0f0;
  background: #fff;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 搜索表单固定高度，防止被压缩 */
:deep(.search-form-content) {
  flex-shrink: 0;
  max-height: 120px; /* 限制搜索表单的最大高度 */
  overflow: visible;
}

/* 针对较小屏幕的响应式处理 */
@media (max-height: 768px) {
  .data-table-wrapper {
    max-height: calc(100vh - 180px);
  }

  .data-table-wrapper :deep(.el-table .el-table__body-wrapper) {
    max-height: calc(100vh - 260px) !important;
  }
}

@media (max-height: 600px) {
  .data-table-wrapper {
    max-height: calc(100vh - 160px);
  }

  .data-table-wrapper :deep(.el-table .el-table__body-wrapper) {
    max-height: calc(100vh - 240px) !important;
  }
}
</style>
