# SearchForm 搜索表单组件使用指南

## 概述

SearchForm 是一个基于 schema 配置的通用搜索表单组件，支持多种表单控件类型，通过 JSON 配置即可快速构建搜索表单。

## 特性

- 🎯 **Schema 驱动**: 通过 JSON 配置定义表单字段
- 🧩 **多种控件**: 支持输入框、选择器、日期选择器等多种控件
- 🔄 **双向绑定**: 支持 v-model 双向数据绑定
- 🎨 **响应式设计**: 自适应不同屏幕尺寸
- 🛠 **高度可定制**: 支持自定义插槽和样式
- 📱 **移动端友好**: 在移动设备上自动调整布局

## 基本用法

```vue
<template>
  <SearchForm
    v-model="searchForm"
    :fields="searchFields"
    @search="handleSearch"
    @reset="handleReset"
  />
</template>

<script setup>
import SearchForm from '@/components/common/SearchForm.vue'
import { createField, COMMON_OPTIONS } from '@/types/searchForm.js'

const searchForm = ref({})

const searchFields = [
  createField.input('keyword', '关键词'),
  createField.select('status', '状态', COMMON_OPTIONS.STATUS_OPTIONS),
  createField.dateRange('dateRange', '日期范围'),
]

const handleSearch = (formData) => {
  console.log('搜索参数:', formData)
}

const handleReset = (formData) => {
  console.log('重置后的数据:', formData)
}
</script>
```

## 支持的字段类型

### 1. 输入框 (input)

```javascript
createField.input('keyword', '关键词', {
  placeholder: '请输入关键词',
  width: '200px',
  clearable: true,
  disabled: false,
})
```

### 2. 选择器 (select)

```javascript
createField.select('status', '状态', [
  { label: '全部', value: '' },
  { label: '有效', value: '1' },
  { label: '无效', value: '0' },
], {
  width: '150px',
  clearable: true,
})
```

### 3. 日期选择器 (date)

```javascript
createField.date('publishDate', '发布日期', {
  width: '200px',
  placeholder: '请选择日期',
})
```

### 4. 日期范围选择器 (daterange)

```javascript
createField.dateRange('dateRange', '日期范围', {
  width: '300px',
})
```

### 5. 数字输入框 (number)

```javascript
createField.number('amount', '金额', {
  min: 0,
  max: 999999,
  step: 1,
  width: '200px',
})
```

### 6. 级联选择器 (cascader)

```javascript
{
  type: 'cascader',
  prop: 'region',
  label: '地区',
  options: [
    {
      value: 'beijing',
      label: '北京',
      children: [
        { value: 'haidian', label: '海淀区' },
        { value: 'chaoyang', label: '朝阳区' },
      ]
    }
  ],
  width: '200px',
}
```

### 7. 多选框组 (checkbox)

```javascript
{
  type: 'checkbox',
  prop: 'features',
  label: '特性',
  options: [
    { label: '紧急', value: 'urgent' },
    { label: '重要', value: 'important' },
  ],
}
```

### 8. 单选框组 (radio)

```javascript
{
  type: 'radio',
  prop: 'priority',
  label: '优先级',
  options: [
    { label: '高', value: 'high' },
    { label: '中', value: 'medium' },
    { label: '低', value: 'low' },
  ],
}
```

### 9. 自定义插槽 (slot)

```javascript
{
  type: 'slot',
  prop: 'custom',
  label: '自定义',
}
```

```vue
<template>
  <SearchForm :fields="fields">
    <template #custom="{ value, setValue }">
      <el-input
        :model-value="value"
        @update:model-value="setValue"
        placeholder="自定义输入框"
      />
    </template>
  </SearchForm>
</template>
```

## 组件属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| fields | Array | [] | 字段配置数组 |
| modelValue | Object | {} | 表单数据 |
| inline | Boolean | true | 是否内联显示 |
| labelWidth | String | '80px' | 标签宽度 |
| showActions | Boolean | true | 是否显示操作按钮 |
| searchText | String | '查询' | 搜索按钮文本 |
| resetText | String | '重置' | 重置按钮文本 |

## 组件事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| search | formData | 点击搜索按钮时触发 |
| reset | formData | 点击重置按钮时触发 |
| update:modelValue | formData | 表单数据变化时触发 |

## 常用选项配置

```javascript
import { COMMON_OPTIONS } from '@/types/searchForm.js'

// 状态选项
COMMON_OPTIONS.STATUS_OPTIONS

// 是否选项
COMMON_OPTIONS.YES_NO_OPTIONS

// 公告类型选项
COMMON_OPTIONS.ANNOUNCEMENT_TYPE_OPTIONS

// 采购方式选项
COMMON_OPTIONS.PROCUREMENT_METHOD_OPTIONS
```

## 完整示例

```vue
<template>
  <div class="page">
    <SearchForm
      v-model="searchForm"
      :fields="searchFields"
      @search="handleSearch"
      @reset="handleReset"
    >
      <!-- 自定义操作按钮 -->
      <template #actions="{ formData }">
        <el-button type="success" @click="handleExport(formData)">
          导出
        </el-button>
      </template>
    </SearchForm>
    
    <DataTable
      :columns="columns"
      :data="tableData"
      :loading="loading"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import SearchForm from '@/components/common/SearchForm.vue'
import DataTable from '@/components/common/DataTable.vue'
import { createField, COMMON_OPTIONS } from '@/types/searchForm.js'

const searchForm = ref({})

const searchFields = [
  createField.input('keyword', '关键词', {
    placeholder: '请输入关键词搜索',
    width: '200px',
  }),
  createField.select('type', '类型', COMMON_OPTIONS.ANNOUNCEMENT_TYPE_OPTIONS),
  createField.dateRange('dateRange', '日期范围'),
  createField.select('status', '状态', COMMON_OPTIONS.STATUS_OPTIONS),
]

const handleSearch = (formData) => {
  console.log('搜索:', formData)
  // 执行搜索逻辑
}

const handleReset = (formData) => {
  console.log('重置:', formData)
  // 执行重置逻辑
}

const handleExport = (formData) => {
  console.log('导出:', formData)
  // 执行导出逻辑
}
</script>
```

## 样式定制

组件提供了丰富的 CSS 变量和类名，可以轻松定制样式：

```css
.search-form {
  /* 自定义背景色 */
  background: #f0f2f5;
  
  /* 自定义边框 */
  border-left-color: #52c41a;
}

.search-form-content :deep(.el-form-item) {
  /* 自定义表单项间距 */
  margin-bottom: 20px;
  margin-right: 30px;
}
```
