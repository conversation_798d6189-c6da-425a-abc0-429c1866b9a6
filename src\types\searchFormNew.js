/**
 * 新版搜索表单字段类型定义
 * 使用简化的对象配置格式
 */

/**
 * 基础字段配置
 * @typedef {Object} BaseField
 * @property {string} type - 字段类型
 * @property {string} name - 字段名称
 * @property {string} label - 字段标签
 * @property {string} [placeholder] - 占位符文本
 * @property {boolean} [required=false] - 是否必填
 * @property {boolean} [clearable=true] - 是否可清空
 * @property {boolean} [disabled=false] - 是否禁用
 * @property {any} [defaultValue] - 默认值
 * @property {Array} [validators] - 验证规则
 */

/**
 * 文本输入框字段
 * @typedef {BaseField} TextField
 * @property {'text'} type
 */

/**
 * 文本域字段
 * @typedef {BaseField} TextareaField
 * @property {'textarea'} type
 * @property {number} [rows=3] - 行数
 * @property {Object} [validators] - 验证规则，如 { minlength: 8 }
 */

/**
 * 选择器字段
 * @typedef {BaseField} SelectField
 * @property {'select'} type
 * @property {Array<{label: string, value: any}>} options - 选项列表
 */

/**
 * 日期选择器字段
 * @typedef {BaseField} DateField
 * @property {'date'} type
 */

/**
 * 日期范围选择器字段
 * @typedef {BaseField} DateRangeField
 * @property {'daterange'} type
 */

/**
 * 数字输入框字段
 * @typedef {BaseField} NumberField
 * @property {'number'} type
 * @property {number} [min] - 最小值
 * @property {number} [max] - 最大值
 * @property {number} [step=1] - 步长
 */

/**
 * 树形选择器字段
 * @typedef {BaseField} TreeField
 * @property {'tree'} type
 * @property {Array} datasrc - 树形数据源
 * @property {number} [level] - 选择层级
 */

/**
 * 级联选择器字段
 * @typedef {BaseField} CascaderField
 * @property {'cascader'} type
 * @property {Array} options - 级联选项
 * @property {Object} [props] - 级联配置
 */

/**
 * 多选框组字段
 * @typedef {BaseField} CheckboxField
 * @property {'checkbox'} type
 * @property {Array<{label: string, value: any}>} options - 选项列表
 */

/**
 * 单选框组字段
 * @typedef {BaseField} RadioField
 * @property {'radio'} type
 * @property {Array<{label: string, value: any}>} options - 选项列表
 */

/**
 * 自定义插槽字段
 * @typedef {BaseField} SlotField
 * @property {'slot'} type
 */

// 常用的选项配置
export const OPTIONS = {
  // 状态选项
  STATUS: [
    { label: '全部', value: '' },
    { label: '有效', value: '1' },
    { label: '无效', value: '0' },
  ],
  
  // 是否选项
  YES_NO: [
    { label: '全部', value: '' },
    { label: '是', value: '1' },
    { label: '否', value: '0' },
  ],
  
  // 公告类型选项
  ANNOUNCEMENT_TYPE: [
    { label: '全部', value: '' },
    { label: '招标公告', value: 'tender' },
    { label: '中标公告', value: 'award' },
    { label: '变更公告', value: 'change' },
    { label: '废标公告', value: 'cancel' },
  ],
  
  // 采购方式选项
  PROCUREMENT_METHOD: [
    { label: '全部', value: '' },
    { label: '公开招标', value: 'open' },
    { label: '邀请招标', value: 'invite' },
    { label: '竞争性谈判', value: 'negotiate' },
    { label: '单一来源', value: 'single' },
    { label: '询价', value: 'inquiry' },
  ],
}

// 常用验证规则
export const VALIDATORS = {
  // 手机号正则
  MOBILE: /^1[3-9]\d{9}$/,
  
  // 邮箱正则
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  
  // 身份证号正则
  ID_CARD: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
  
  // 统一社会信用代码
  CREDIT_CODE: /^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/,
}

// 字段创建工具函数
export const createField = {
  /**
   * 创建文本输入框字段
   * @param {string} name 字段名
   * @param {string} label 标签
   * @param {Object} options 其他配置
   * @returns {TextField}
   */
  text: (name, label, options = {}) => ({
    type: 'text',
    name,
    label,
    ...options,
  }),

  /**
   * 创建文本域字段
   * @param {string} name 字段名
   * @param {string} label 标签
   * @param {Object} options 其他配置
   * @returns {TextareaField}
   */
  textarea: (name, label, options = {}) => ({
    type: 'textarea',
    name,
    label,
    rows: 3,
    ...options,
  }),

  /**
   * 创建选择器字段
   * @param {string} name 字段名
   * @param {string} label 标签
   * @param {Array} options 选项列表
   * @param {Object} config 其他配置
   * @returns {SelectField}
   */
  select: (name, label, options, config = {}) => ({
    type: 'select',
    name,
    label,
    options,
    ...config,
  }),

  /**
   * 创建日期字段
   * @param {string} name 字段名
   * @param {string} label 标签
   * @param {Object} options 其他配置
   * @returns {DateField}
   */
  date: (name, label, options = {}) => ({
    type: 'date',
    name,
    label,
    ...options,
  }),

  /**
   * 创建日期范围字段
   * @param {string} name 字段名
   * @param {string} label 标签
   * @param {Object} options 其他配置
   * @returns {DateRangeField}
   */
  dateRange: (name, label, options = {}) => ({
    type: 'daterange',
    name,
    label,
    ...options,
  }),

  /**
   * 创建数字输入框字段
   * @param {string} name 字段名
   * @param {string} label 标签
   * @param {Object} options 其他配置
   * @returns {NumberField}
   */
  number: (name, label, options = {}) => ({
    type: 'number',
    name,
    label,
    ...options,
  }),

  /**
   * 创建树形选择器字段
   * @param {string} name 字段名
   * @param {string} label 标签
   * @param {Array} datasrc 数据源
   * @param {Object} options 其他配置
   * @returns {TreeField}
   */
  tree: (name, label, datasrc, options = {}) => ({
    type: 'tree',
    name,
    label,
    datasrc,
    ...options,
  }),
}

// 示例配置
export const EXAMPLE_FIELDS = [
  {
    type: 'text',
    name: 'keyword',
    label: '关键词',
    placeholder: '请输入关键词',
  },
  {
    type: 'textarea',
    name: 'content',
    label: '内容',
    validators: {
      minlength: 8
    }
  },
  {
    type: 'tree',
    name: 'address',
    label: '地址',
    datasrc: 'areaTree',
    level: 3
  },
  {
    type: 'text',
    name: 'contact',
    label: '联系方式',
    required: true,
    validators: {
      regexp: VALIDATORS.MOBILE,
    }
  },
  {
    type: 'select',
    name: 'status',
    label: '状态',
    options: OPTIONS.STATUS,
  },
  {
    type: 'daterange',
    name: 'dateRange',
    label: '日期范围',
  },
]
