import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/store/modules/auth'

const routes = [
  // 访客路由
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/visitor/Home.vue'),
    meta: { title: '首页' },
  },
  {
    path: '/bid-announcement',
    name: 'BidAnnouncement',
    component: () => import('@/views/visitor/BidAnnouncement.vue'),
    meta: {
      title: '竞价公告',
      useUnifiedCard: true,
      showPageHeader: true,
    },
  },
  {
    path: '/change-announcement',
    name: 'ChangeAnnouncement',
    component: () => import('@/views/visitor/ChangeAnnouncement.vue'),
    meta: { title: '变更公告' },
  },
  {
    path: '/result-announcement',
    name: 'ResultAnnouncement',
    component: () => import('@/views/visitor/ResultAnnouncement.vue'),
    meta: { title: '结果公告' },
  },
  {
    path: '/announcement/:id',
    name: 'AnnouncementDetail',
    component: () => import('@/views/visitor/AnnouncementDetail.vue'),
    meta: { title: '公告详情' },
  },

  // 演示路由
  {
    path: '/demo/table-search',
    name: 'TableSearchDemo',
    component: () => import('@/views/demo/TableSearchDemo.vue'),
    meta: { title: '表格搜索演示' },
  },

  // 认证路由
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/Login.vue'),
    meta: { title: '登录', hideHeader: true },
  },
  {
    path: '/register',
    name: 'SupplierRegister',
    component: () => import('@/views/auth/SupplierRegister.vue'),
    meta: { title: '供应商注册', hideHeader: true },
  },

  // 供应商路由
  {
    path: '/supplier',
    redirect: '/supplier/bid-participation',
    meta: { requiresAuth: true, role: 'supplier' },
  },
  {
    path: '/supplier/bid-participation',
    name: 'BidParticipation',
    component: () => import('@/views/supplier/BidParticipation.vue'),
    meta: { requiresAuth: true, role: 'supplier', title: '竞价参与' },
  },
  {
    path: '/supplier/qualification',
    name: 'Qualification',
    component: () => import('@/views/supplier/Qualification.vue'),
    meta: { requiresAuth: true, role: 'supplier', title: '资格审核' },
  },
  {
    path: '/supplier/bid-history',
    name: 'BidHistory',
    component: () => import('@/views/supplier/BidHistory.vue'),
    meta: { requiresAuth: true, role: 'supplier', title: '竞价历史' },
  },

  // 采购方路由
  {
    path: '/purchaser',
    redirect: '/purchaser/bid-management',
    meta: { requiresAuth: true, role: 'purchaser' },
  },
  {
    path: '/purchaser/bid-management',
    name: 'BidManagement',
    component: () => import('@/views/purchaser/BidManagement.vue'),
    meta: { requiresAuth: true, role: 'purchaser', title: '竞价管理' },
  },
  {
    path: '/purchaser/change-management',
    name: 'ChangeManagement',
    component: () => import('@/views/purchaser/ChangeManagement.vue'),
    meta: { requiresAuth: true, role: 'purchaser', title: '变更管理' },
  },
  {
    path: '/purchaser/result-management',
    name: 'ResultManagement',
    component: () => import('@/views/purchaser/ResultManagement.vue'),
    meta: { requiresAuth: true, role: 'purchaser', title: '结果管理' },
  },
  {
    path: '/purchaser/bid-detail/:id',
    name: 'BidDetail',
    component: () => import('@/views/purchaser/BidDetail.vue'),
    meta: { requiresAuth: true, role: 'purchaser', title: '竞价详情' },
  },

  // 管理员路由
  {
    path: '/admin',
    redirect: '/admin/supplier-approval',
    meta: { requiresAuth: true, role: 'admin' },
  },
  {
    path: '/admin/supplier-approval',
    name: 'SupplierApproval',
    component: () => import('@/views/admin/SupplierApproval.vue'),
    meta: { requiresAuth: true, role: 'admin', title: '供应商审批' },
  },
  {
    path: '/admin/config-management',
    name: 'ConfigManagement',
    component: () => import('@/views/admin/ConfigManagement.vue'),
    meta: { requiresAuth: true, role: 'admin', title: '系统配置' },
  },

  // 通用路由
  {
    path: '/search',
    name: 'SearchResults',
    component: () => import('@/views/common/SearchResults.vue'),
    meta: { title: '搜索结果' },
  },
  {
    path: '/profile',
    name: 'Profile',
    component: () => import('@/views/common/Profile.vue'),
    meta: { requiresAuth: true, title: '个人中心' },
  },
  {
    path: '/settings',
    name: 'Settings',
    component: () => import('@/views/common/Settings.vue'),
    meta: { requiresAuth: true, title: '设置' },
  },

  // 错误页面
  {
    path: '/403',
    name: 'Forbidden',
    component: () => import('@/views/error/403.vue'),
    meta: { title: '无权限访问' },
  },
  {
    path: '/404',
    name: 'NotFound',
    component: () => import('@/views/error/404.vue'),
    meta: { title: '页面不存在' },
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404',
  },
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  },
})

// 路由守卫 - 权限控制
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  // 初始化认证状态
  if (!authStore.isAuthenticated && localStorage.getItem('token')) {
    authStore.initAuth()
  }

  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - MCP-BID`
  }

  // 检查是否需要认证
  if (to.meta.requiresAuth) {
    if (!authStore.isAuthenticated) {
      next({
        path: '/login',
        query: { redirect: to.fullPath },
      })
      return
    }

    // 检查角色权限
    if (to.meta.role && authStore.userRole !== to.meta.role) {
      next('/403')
      return
    }

    // 检查具体权限
    if (to.meta.permission && !authStore.hasPermission(to.meta.permission)) {
      next('/403')
      return
    }
  }

  // 已登录用户访问登录页面，重定向到首页
  if (
    (to.path === '/login' || to.path === '/register') &&
    authStore.isAuthenticated
  ) {
    next('/')
    return
  }

  next()
})

// 路由错误处理
router.onError(() => {
  // console.error('路由错误:', error)
})

export default router
