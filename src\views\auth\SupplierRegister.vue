<template>
  <div class="supplier-register">
    <div class="container">
      <h1 class="title">供应商注册</h1>

      <!-- 步骤指示器 -->
      <div class="steps">
        <div
          class="step-item"
          :class="{ active: currentStep === 1, completed: currentStep > 1 }"
        >
          <div class="step-number">1</div>
          <div class="step-title">填写机构信息</div>
        </div>
        <div
          class="step-item"
          :class="{ active: currentStep === 2, completed: currentStep > 2 }"
        >
          <div class="step-number">2</div>
          <div class="step-title">上传证照</div>
        </div>
      </div>

      <!-- 步骤1: 填写机构信息 -->
      <div v-if="currentStep === 1" class="step-content">
        <el-form ref="formRef" :model="formData" label-width="120px">
          <el-row :gutter="40">
            <el-col :span="12">
              <el-form-item label="机构名称">
                <el-input
                  v-model="formData.orgName"
                  placeholder="请输入机构名称"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="机构统一社会信用代码">
                <el-input
                  v-model="formData.creditCode"
                  placeholder="请输入统一社会信用代码"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="40">
            <el-col :span="12">
              <el-form-item label="机构所属区域">
                <el-select
                  v-model="formData.region"
                  placeholder="请选择所属区域"
                >
                  <el-option label="北京市" value="beijing" />
                  <el-option label="上海市" value="shanghai" />
                  <el-option label="广州市" value="guangzhou" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="机构地址">
                <el-input
                  v-model="formData.address"
                  placeholder="请输入机构地址"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="40">
            <el-col :span="12">
              <el-form-item label="经营种类">
                <el-select
                  v-model="formData.businessType"
                  placeholder="请选择经营种类"
                >
                  <el-option label="批发业务" value="wholesale" />
                  <el-option label="零售业务" value="retail" />
                  <el-option label="服务业务" value="service" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="主要商品">
                <el-input
                  v-model="formData.mainProducts"
                  placeholder="请输入主要商品"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="40">
            <el-col :span="12">
              <el-form-item label="法人">
                <el-input
                  v-model="formData.legalPerson"
                  placeholder="请输入法人姓名"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="联系人">
                <el-input
                  v-model="formData.contact"
                  placeholder="请输入联系人姓名"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="40">
            <el-col :span="12">
              <el-form-item label="手机号码">
                <el-input
                  v-model="formData.phone"
                  placeholder="请输入手机号码"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="联系电话">
                <el-input
                  v-model="formData.telephone"
                  placeholder="请输入联系电话"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="40">
            <el-col :span="12">
              <el-form-item label="电子邮箱">
                <el-input
                  v-model="formData.email"
                  placeholder="请输入电子邮箱"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="传真">
                <el-input v-model="formData.fax" placeholder="请输入传真号码" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="40">
            <el-col :span="12">
              <el-form-item label="平台业务">
                <el-select
                  v-model="formData.platformBusiness"
                  placeholder="请选择平台业务"
                >
                  <el-option label="招采业务" value="bidding" />
                  <el-option label="采购业务" value="procurement" />
                  <el-option label="供应业务" value="supply" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="服务机构">
                <el-input
                  v-model="formData.serviceOrg"
                  placeholder="请输入服务机构"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

        <div class="step-actions">
          <el-button type="primary" @click="nextStep">下一步</el-button>
        </div>
      </div>

      <!-- 步骤2: 上传证照 -->
      <div v-if="currentStep === 2" class="step-content">
        <div class="upload-section">
          <div class="upload-item">
            <div class="upload-label-wrapper">
              <label class="upload-label required">营业执照证件:</label>
              <el-upload
                class="simple-upload"
                :action="uploadUrl"
                :headers="uploadHeaders"
                accept=".jpg,.jpeg,.png,.pdf"
                :show-file-list="false"
                :before-upload="beforeUpload"
                :on-success="
                  (res) => handleUploadSuccess(res, 'businessLicense')
                "
                :on-error="handleUploadError"
                drag
              >
                <div class="upload-content">
                  <el-icon class="upload-icon"><Plus /></el-icon>
                  <div class="upload-text">上传</div>
                </div>
              </el-upload>
            </div>
          </div>

          <div class="upload-item">
            <div class="upload-label-wrapper">
              <label class="upload-label required">经营许可证:</label>
              <el-upload
                class="simple-upload"
                :action="uploadUrl"
                :headers="uploadHeaders"
                accept=".jpg,.jpeg,.png,.pdf"
                :show-file-list="false"
                :before-upload="beforeUpload"
                :on-success="
                  (res) => handleUploadSuccess(res, 'businessPermit')
                "
                :on-error="handleUploadError"
                drag
              >
                <div class="upload-content">
                  <el-icon class="upload-icon"><Plus /></el-icon>
                  <div class="upload-text">上传</div>
                </div>
              </el-upload>
            </div>
          </div>

          <div class="upload-item">
            <div class="upload-label-wrapper">
              <label class="upload-label required">法人证件:</label>
              <div class="id-card-uploads">
                <div class="id-card-side">
                  <el-upload
                    class="simple-upload"
                    :action="uploadUrl"
                    :headers="uploadHeaders"
                    accept=".jpg,.jpeg,.png"
                    :show-file-list="false"
                    :before-upload="beforeUpload"
                    :on-success="
                      (res) =>
                        handleUploadSuccess(res, 'legalPersonIdCardFront')
                    "
                    :on-error="handleUploadError"
                    drag
                  >
                    <div class="upload-content">
                      <el-icon class="upload-icon"><Plus /></el-icon>
                      <div class="upload-text">上传</div>
                    </div>
                  </el-upload>
                  <div class="upload-desc">正面</div>
                </div>
                <div class="id-card-side">
                  <el-upload
                    class="simple-upload"
                    :action="uploadUrl"
                    :headers="uploadHeaders"
                    accept=".jpg,.jpeg,.png"
                    :show-file-list="false"
                    :before-upload="beforeUpload"
                    :on-success="
                      (res) => handleUploadSuccess(res, 'legalPersonIdCardBack')
                    "
                    :on-error="handleUploadError"
                    drag
                  >
                    <div class="upload-content">
                      <el-icon class="upload-icon"><Plus /></el-icon>
                      <div class="upload-text">上传</div>
                    </div>
                  </el-upload>
                  <div class="upload-desc">反面</div>
                </div>
              </div>
            </div>
            <div class="upload-note">说明：法人证件请上传正反面</div>
          </div>
        </div>

        <div class="step-actions">
          <el-button type="primary" @click="submitForm">注册</el-button>
        </div>
      </div>
    </div>

    <!-- 注册成功弹窗 -->
    <!-- <RegisterSuccessDialog
      v-model="showSuccessDialog"
      @confirm="handleSuccessConfirm"
    /> -->
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
// import RegisterSuccessDialog from '@/components/common/RegisterSuccessDialog.vue'

defineOptions({
  name: 'SupplierRegister',
})

const currentStep = ref(1)
const formRef = ref()
const showSuccessDialog = ref(false)

const formData = reactive({
  orgName: '',
  creditCode: '',
  region: '',
  address: '',
  businessType: '',
  mainProducts: '',
  legalPerson: '',
  contact: '',
  phone: '',
  telephone: '',
  email: '',
  fax: '',
  platformBusiness: '',
  serviceOrg: '',
})

// 暂时注释掉校验规则
// const rules = {
//   orgName: [{ required: true, message: '请输入机构名称', trigger: 'blur' }],
//   creditCode: [
//     { required: true, message: '请输入统一社会信用代码', trigger: 'blur' },
//   ],
//   region: [{ required: true, message: '请选择所属区域', trigger: 'change' }],
//   address: [{ required: true, message: '请输入机构地址', trigger: 'blur' }],
//   contact: [{ required: true, message: '请输入联系人姓名', trigger: 'blur' }],
//   phone: [
//     { required: true, message: '请输入手机号码', trigger: 'blur' },
//     { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' },
//   ],
//   email: [
//     { required: true, message: '请输入电子邮箱', trigger: 'blur' },
//     { type: 'email', message: '电子邮箱格式不正确', trigger: 'blur' },
//   ],
//   platformBusiness: [
//     { required: true, message: '请选择平台业务', trigger: 'change' },
//   ],
// }

const nextStep = () => {
  // 暂时取消表单验证
  currentStep.value = 2
  // formRef.value.validate((valid) => {
  //   if (valid) {
  //     currentStep.value = 2
  //   }
  // })
}

// const prevStep = () => {
//   currentStep.value = 1
// }

const submitForm = () => {
  // console.log('提交', formData.businessLicense)

  // 验证必传证照是否已上传
  if (!formData.businessLicense) {
    ElMessage({
      message: '请上传营业执照证件!',
      type: 'error',
    })
    return
  }

  if (!formData.businessPermit) {
    ElMessage({
      message: '请上传经营许可证件!',
      type: 'error',
    })
    return
  }

  if (!formData.legalPersonIdCardFront) {
    ElMessage({
      message: '请上传法人证件正面!',
      type: 'error',
    })
    return
  }

  if (!formData.legalPersonIdCardBack) {
    ElMessage({
      message: '请上传法人证件反面!',
      type: 'error',
    })
    return
  }

  // 模拟提交成功后显示弹窗
  showSuccessDialog.value = true
}

// 上传相关方法
const beforeUpload = (file) => {
  // 检查文件类型
  const isValidType =
    file.type === 'image/jpeg' ||
    file.type === 'image/png' ||
    file.type === 'application/pdf'
  if (!isValidType) {
    ElMessage.error('只能上传 JPG/PNG/PDF 格式的文件!')
    return false
  }

  // 检查文件大小 (5MB)
  const isLt5M = file.size / 1024 / 1024 < 5
  if (!isLt5M) {
    ElMessage.error('文件大小不能超过 5MB!')
    return false
  }

  return true
}

const handleUploadSuccess = (response, field) => {
  // 处理上传成功
  const url = response.url || response.data?.url || ''
  if (url) {
    formData[field].push(url)
    ElMessage.success('文件上传成功!')
  }
}

const handleUploadError = () => {
  ElMessage.error('文件上传失败!')
}
</script>

<style lang="scss" scoped>
.supplier-register {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 40px 0;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    background: white;
    border-radius: 8px;
    padding: 40px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  }

  .title {
    text-align: center;
    font-size: 28px;
    color: #333;
    margin-bottom: 40px;
    font-weight: 500;
  }

  .steps {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 40px;
    position: relative;

    .step-item {
      display: flex;
      align-items: center;
      padding: 8px 30px;
      position: relative;
      clip-path: polygon(0 0, 95% 0, 100% 50%, 95% 100%, 0 100%);
      min-width: 200px;
      height: 40px;

      // 默认状态（步骤1 - 当前活动状态）
      &:first-child {
        background: #2897e0;
        color: #ffffff;
        z-index: 2;

        &.completed {
          background: #ffffff;
          color: #333333;
          border: 1px solid #e4e7ed;

          .step-number {
            background: #ffffff;
            color: #333333;
            // border: 2px solid #333333;
          }

          .step-title {
            color: #333333;
          }
        }

        .step-number {
          // background: #ffffff;
          color: #ffffff;
          // border: 2px solid #ffffff;
        }

        .step-title {
          color: #ffffff;
        }
      }

      // 默认状态（步骤2 - 未激活状态）
      &:last-child {
        background: #f5f5f5;
        color: #8c8c8c;
        margin-left: -20px;
        z-index: 1;

        &.active {
          background: #2897e0;
          color: #ffffff;

          .step-title {
            color: #ffffff;
          }
        }

        .step-number {
          // background: #ffffff;
          color: #333333;
          // border: 2px solid #333333;
        }

        .step-title {
          color: #8c8c8c;
        }
      }

      .step-number {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        font-size: 22px;
        font-weight: 500;
        margin-right: 8px;
        flex-shrink: 0;
      }

      .step-title {
        font-size: 14px;
        font-weight: 500;
        white-space: nowrap;
      }
    }
  }

  .step-content {
    margin-bottom: 40px;
  }

  .error-text {
    color: #f56c6c;
    font-size: 12px;
    margin-top: 5px;
  }

  .upload-section {
    .upload-item {
      margin-bottom: 40px;

      .upload-label-wrapper {
        display: flex;
        align-items: flex-start;
        gap: 20px;

        .upload-label {
          min-width: 120px;
          font-size: 16px;
          color: #333;
          font-weight: 500;
          line-height: 1.5;
          margin-top: 8px;

          &.required::before {
            content: '*';
            color: #f56c6c;
            margin-right: 5px;
          }
        }
      }

      .id-card-uploads {
        display: flex;
        gap: 40px;
        flex-wrap: wrap;

        .id-card-side {
          display: flex;
          flex-direction: column;
          align-items: center;

          .upload-desc {
            text-align: center;
            margin-top: 10px;
            font-size: 14px;
            color: #606266;
          }
        }
      }

      .upload-note {
        margin-top: 10px;
        margin-left: 140px;
        font-size: 12px;
        color: #f56c6c;
      }
    }
  }

  .step-actions {
    text-align: center;
    padding-top: 40px;
    border-top: 1px solid #e4e7ed;

    .el-button {
      margin: 0 10px;
      padding: 12px 40px;
      font-size: 16px;
    }
  }
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #333;
}

:deep(.el-input__inner) {
  height: 40px;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-upload-dragger) {
  width: 100%;
  height: 100%;
  border: none;
  border-radius: 6px;
  background: transparent;
}

// 上传组件样式
.simple-upload {
  :deep(.el-upload) {
    display: block;
  }

  :deep(.el-upload-dragger) {
    padding: 15px;
    border: 2px dashed #dcdfe6;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: border-color 0.2s;
    background: #fafafa;
    width: 200px;
    height: 120px;

    &:hover {
      border-color: #409eff;
    }
  }

  .upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #8c939d;

    .upload-icon {
      font-size: 24px;
      margin-bottom: 8px;
    }

    .upload-text {
      font-size: 14px;
      text-align: center;
    }
  }
}
</style>
